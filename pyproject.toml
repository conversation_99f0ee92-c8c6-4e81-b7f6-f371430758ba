[project]
name = "LLMFlowHub"
version = "0.0.1"
description = "智能信息处理与LLM能力集成的API中枢。"
requires-python = ">=3.10"
dependencies = [
    "uv>=0.8.9",
    "fastapi>=0.116.1",
    "pydantic>=2.11.7",
    "pydantic-settings>=2.10.1",
    "python-dotenv>=1.1.1",
    "uvicorn>=0.35.0",
    "click>=8.2.1",
    "python-multipart>=0.0.20",
    "beautifulsoup4>=4.13.4",
    "json-repair>=0.49.0",
    "celery[redis]>=5.5.3",
    "langchain>=0.3.27",
    "langchain-core==0.3.74",
    "langchain-openai>=0.3.30",
    "sqlalchemy>=2.0.43",
    "sqlalchemy-utils>=0.41.2",
    "pymysql>=1.1.1",
    "pyjwt==2.10.1",
    "tenacity>=9.1.2",
    "celery-stubs==0.1.3",
    "flower>=2.0.1"
]

[dependency-groups]
dev = [
    "pytest>=8.4.1",
]
