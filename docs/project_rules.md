# 项目开发规范与架构指南

本文档旨在为 LlmFlowHub 项目的开发者提供清晰的开发规范、架构概览和核心设计原则，确保代码质量、可维护性和团队协作效率。

## 1. 项目概述 (Project Overview)

LlmFlowHub 是一个基于 FastAPI 构建的后端服务，专注于提供高效、可扩展的 LLM（大语言模型）集成能力。它通过 LangChain 框架与 OpenAI 兼容的 LLM 进行交互，并利用 Celery 处理异步任务，实现长文本处理、事件提取、前沿动态分析等复杂功能。项目采用分层架构，强调模块化、可配置性和健壮性。

## 2. 技术栈 (Technology Stack)

*   **Web 框架**: FastAPI (高性能异步 Web 框架)
*   **异步任务队列**: Celery (配合 Redis 作为 Broker 和 Backend)
*   **LLM 框架**: LangChain (用于构建 LLM 应用的框架)
*   **LLM 提供商**: OpenAI API (通过 `langchain-openai` 兼容)
*   **ORM (对象关系映射)**: SQLAlchemy (用于数据库交互，支持 MySQL)
*   **数据验证与设置**: Pydantic (用于数据模型定义、验证和应用配置管理)
*   **重试机制**: Tenacity (提供强大的重试和指数退避策略)
*   **日志**: Python `logging` 模块 (通过 `app.core.logger_config` 统一配置)
*   **环境管理**: uv (用于依赖管理和虚拟环境)

## 3. 项目结构 (Project Structure)

```
LlmFlowHub/
├── app/
│   ├── api/                  # API 接口层
│   │   ├── endpoints/        # 各功能模块的 API 端点定义 (e.g., event_extraction.py)
│   │   ├── schemas/          # API 请求/响应 Pydantic 模型
│   │   └── router.py         # 根路由，聚合所有子路由
│   ├── core/                 # 核心配置与工具
│   │   ├── config.py         # 应用配置 (Pydantic Settings)
│   │   ├── logger_config.py  # 日志配置
│   │   └── templates.py      # 模板文件 (如果存在)
│   ├── db/                   # 数据库相关
│   │   ├── database.py       # 数据库连接与会话管理
│   │   ├── models.py         # SQLAlchemy 数据库模型定义
│   │   └── migrations/       # 数据库迁移脚本 (如果使用 Alembic 等)
│   ├── llm_integrations/     # LLM 集成核心模块
│   │   ├── callbacks/        # LLM 链回调处理器 (用于统计、监控)
│   │   ├── chains/           # 核心 LLM 链实现 (Map-Reduce 逻辑)
│   │   ├── configs/          # LLM 链配置 (BaseChainConfig, EventExtractionConfig 等)
│   │   ├── parsers/          # LLM 输出解析器 (JSON 修复与 Pydantic 验证)
│   │   ├── prompts/          # LLM 提示词定义 (Map/Combine 阶段提示词)
│   │   ├── schemas/          # LLM 链内部 Pydantic 模型
│   │   └── llm_models.py     # LLM 模型实例化与管理
│   ├── services/             # 业务逻辑层
│   │   ├── event_extraction.py # 事件提取服务
│   │   ├── frontier_insights.py # 前沿动态服务
│   │   ├── nearby_hotspot.py # 周边热点服务
│   │   └── task_service.py   # 任务管理服务
│   ├── tasks/                # Celery 异步任务定义
│   │   ├── celery_app.py     # Celery 应用实例
│   │   ├── llm_tasks.py      # LLM 相关异步任务
│   │   └── callback_tasks.py # 异步回调任务
│   └── utils/                # 通用工具函数
│       ├── json_extractor.py # JSON 提取工具
│       ├── llm_output_cleaner.py # LLM 输出清理工具
│       ├── redis_utils.py    # Redis 工具
│       └── str_utils.py      # 字符串工具
├── docs/                     # 项目文档
├── logs/                     # 日志文件
├── tests/                    # 单元测试与集成测试
├── run.py                    # 应用启动脚本
├── run_worker.py             # Celery Worker 启动脚本
├── run_flower.py             # Celery Flower 监控启动脚本
├── requirements.txt          # 项目依赖
├── pyproject.toml            # 项目元数据与构建配置
└── .env.example              # 环境变量示例
```

## 4. 核心设计模式与原则 (Core Design Patterns & Principles)

### 4.1. 分层架构 (Layered Architecture)

项目严格遵循分层架构，职责清晰：
*   **API 层 (`app/api`)**: 负责接收 HTTP 请求，进行输入验证，并将请求转发给服务层。不包含业务逻辑。
*   **服务层 (`app/services`)**: 封装核心业务逻辑，协调 LLM 链的调用、数据预处理和结果后处理。
*   **LLM 集成层 (`app/llm_integrations`)**: 抽象 LLM 交互细节，包括链的构建、提示词管理、输出解析、回调和配置。
*   **数据库层 (`app/db`)**: 负责数据持久化，定义数据模型和数据库操作。
*   **任务层 (`app/tasks`)**: 处理异步、耗时任务，与 Celery 队列交互。

### 4.2. API 相关最佳实践 (API Best Practices)

API 层是外部系统与本服务交互的入口，其设计至关重要。

*   **模块化路由**: 
    *   所有 API 端点通过 `app/api/router.py` 中的主路由进行聚合。
    *   每个功能模块（如 `event_extraction`, `frontier_insights`）都有独立的 `APIRouter` 定义在 `app/api/endpoints/` 目录下，并设置了清晰的 `prefix` 和 `tags`。这有助于 API 的组织、版本控制和 Swagger UI 文档的生成。
    *   **示例**: `app/api/endpoints/event_extraction/event_extraction.py` 定义了 `/event-extraction` 前缀下的端点。

*   **Pydantic 数据模型**: 
    *   所有 API 请求体和响应体都必须使用 Pydantic 模型进行定义（位于 `app/api/schemas/`）。
    *   Pydantic 提供了自动的数据验证、序列化和反序列化功能，极大地简化了数据处理，并确保了数据的一致性和类型安全。
    *   **示例**:
        ```python
        # app/api/schemas/my_feature_schemas.py
        from pydantic import BaseModel

        class MyFeatureRequest(BaseModel):
            input_data: str
            param1: int = 0

        class MyFeatureResponse(BaseModel):
            result_data: str
            status_code: int
        ```

*   **统一响应格式**: 
    *   项目采用 `ApiResponse` (位于 `app/api/schemas/response_schemas.py`) 作为统一的 API 响应包装器。
    *   所有成功的 API 响应都应返回 `ApiResponse(status='success', msg='...', data=...)`。
    *   **示例**:
        ```python
        from app.api.schemas.response_schemas import ApiResponse
        # ...
        return ApiResponse(status='success', msg='事件提取成功', data=event_extraction_response)
        ```

*   **健壮的错误处理**: 
    *   API 端点应捕获业务逻辑层抛出的异常，并将其转换为标准的 HTTP 错误响应。
    *   使用 FastAPI 的 `HTTPException` 结合 `ApiResponse` 来返回结构化的错误信息。
    *   **示例**:
        ```python
        from fastapi import HTTPException
        from app.api.schemas.response_schemas import ApiResponse
        # ...
        except Exception as e:
            logger.error(f"事件提取失败: {str(e)}")
            error_response = ApiResponse(status='error', msg='事件提取失败', error=str(e))
            raise HTTPException(status_code=400, detail=error_response)
        ```

### 4.3. LLM 链设计 (LLM Chain Design)

LLM 链是项目的核心智能单元，封装了与大语言模型交互的复杂逻辑，遵循以下设计原则：

*   **模块化与可配置**:
    *   **LLM 模型实例化**: LLM 模型通过 `app/llm_integrations/llm_models.py` 中的 `LlmModel` 类统一实例化。它从 `app/core/config.py` 加载 LLM 的 API 地址、模型名称和密钥，确保配置的灵活性和安全性。
    *   **通用配置**: `app/llm_integrations/configs/base_config.py` 中的 `BaseChainConfig` 定义了所有链的通用配置参数，如重试次数、超时时间、Map-Reduce 开关、分块大小等。
    *   **特定配置**: 特定链（如 `EventExtractionConfig`）继承 `BaseChainConfig`，可扩展自身特有配置，实现细粒度控制。
*   **长文本处理 (Map-Reduce 模式)**:
    *   链能够根据输入文本长度和配置 (`enable_map_reduce`, `chunk_size`) 自动选择单次处理或 Map-Reduce 模式。
    *   **Split (分割)**: 使用 `langchain_text_splitters.RecursiveCharacterTextSplitter` 将长文本分割成大小适中、带有重叠的小块。这有助于处理超长文本，避免 LLM 的上下文窗口限制。
    *   **Map (映射)**: 对每个文本块独立调用 LLM 进行初步提取。每个 Map 任务使用 `map_prompt` (位于 `app/llm_integrations/prompts/`)，该提示词专门设计用于指导 LLM 从小段文本中提取信息，并返回结构化的 JSON 结果。
    *   **Combine (合并与去重)**: 将所有 Map 阶段返回的 JSON 结果进行聚合，并再次调用 LLM 进行最终的整合和**去重**。`combine_prompt` (位于 `app/llm_integrations/prompts/`) 专门设计用于指导 LLM 识别并合并语义相似的事件，保留最完整的信息，生成最终的、无重复的结构化输出。
*   **健壮性与弹性 (Tenacity 重试机制)**:
    *   所有 LLM 链都集成了 `Tenacity` 库提供的重试机制。
    *   它针对 LLM 交互中常见的瞬时错误（如 `OutputParserException`、`json.JSONDecodeError`、`asyncio.TimeoutError`）进行自动重试。
    *   采用指数退避策略，避免对 LLM 服务造成过大压力，提高系统稳定性。
*   **输出解析与验证**:
    *   `app/llm_integrations/parsers/` 模块负责将 LLM 的原始文本输出（通常是 JSON 字符串）解析为结构化的 Pydantic 对象。
    *   解析器具备强大的容错能力，能处理 LLM 输出中可能存在的非标准 JSON 格式（如 Markdown 代码块、不完整 JSON），并利用 `json_repair` 库尝试修复这些问题。
    *   解析后的数据会通过 Pydantic 进行严格的数据验证，确保数据符合预期的 Schema (`app/llm_integrations/schemas/`)，保证数据质量。
*   **提示词管理**:
    *   `prompts` 模块集中管理所有 LLM 提示词，包括 `map_prompt` 和 `combine_prompt`。
    *   提示词设计详细、清晰，明确定义了 LLM 的角色、任务、输出格式和具体规则。
    *   所有提示词都强制要求 LLM 输出简体中文。
*   **可观测性 (Callbacks)**:
    *   `callbacks` 模块定义了 LLM 链的回调处理器。
    *   这些回调用于在链运行过程中收集统计信息（如提取事件数量、Token 消耗、重试次数等），为监控和分析提供数据支持。
    *   回调机制与链的后处理逻辑集成，确保统计数据的准确性。

### 4.4. 异步任务处理 (Asynchronous Task Processing)

项目利用 Celery 框架处理耗时和计算密集型任务，以避免阻塞 FastAPI 主服务的响应，提升用户体验和系统吞吐量。

*   **任务定义**: 
    *   所有异步任务定义在 `app/tasks/llm_tasks.py` 中。
    *   每个任务都是一个 Celery `task`，通过 `@celery_app.task(bind=True, name=...)` 装饰器定义。`bind=True` 允许任务函数访问自身的上下文 (`self`)。
    *   任务函数内部会调用 `app/services/` 目录下的相应业务服务方法。
    *   **示例**: `event_extraction_task` 调用 `app.services.event_extraction.extract_events`。

*   **任务生命周期与状态管理**: 
    *   任务在执行过程中会更新其状态，例如从 `PENDING` 到 `STARTED`，再到 `SUCCESS` 或 `FAILURE`。
    *   通过 `self.update_state(state='STARTED', meta={...})` 可以向 Celery Broker 发送任务状态更新，并附带自定义的元数据（如 `task_id`, `progress`, `error_message` 等）。这对于任务的实时监控和追踪至关重要。
    *   任务结果（成功或失败）也会被 Celery 存储，可以通过 `AsyncResult` 对象查询。

*   **异步回调机制**: 
    *   任务支持可选的 `callback_url` 参数。当任务完成（成功或失败）时，会触发一个异步回调。
    *   回调通过 `app/tasks/callback_tasks.py` 中的 `send_task_callback` 任务发送，确保回调本身也是非阻塞的。
    *   回调数据 (`CallbackSuccessData`, `CallbackFailureData`) 采用 Pydantic 模型进行标准化，确保数据格式的一致性。
    *   **目的**: 允许外部系统或前端在任务完成后接收通知，而无需持续轮询任务状态。

*   **运行与监控**: 
    *   `run_worker.py` 用于启动 Celery Worker 进程，这些 Worker 会从任务队列中拉取任务并执行。
    *   `run_flower.py` 用于启动 Celery Flower 监控面板，提供任务的实时状态、历史记录、Worker 统计等可视化界面，便于运维和调试。

### 4.5. 数据管理 (Data Management)

*   **SQLAlchemy ORM**: 使用 SQLAlchemy 进行数据库交互，通过 `app/db/models.py` 定义数据模型。
*   **LLM 结果持久化**: `LLMResult` 模型用于存储 LLM 处理任务的输入、输出、状态和错误信息，便于任务追踪和结果审计。
*   **Pydantic Schemas**: 广泛用于 API 请求/响应验证、LLM 链内部数据结构和回调数据，确保数据一致性和类型安全。

### 4.6. 配置管理 (Configuration Management)

*   **Pydantic Settings**: 应用配置通过 `app/core/config.py` 中的 Pydantic Settings 管理，支持从环境变量、.env 文件加载配置，确保配置的灵活性和安全性。
*   **LLM 链配置**: LLM 链的特定行为通过 `app/llm_integrations/configs` 模块中的配置类进行管理，实现了细粒度的控制。

## 5. 开发流程 (Development Workflow)

### 5.1. 新增功能模块开发流程 (Development Process for New Feature Modules)

本节提供一个通用的开发流程，指导开发者如何向项目中添加新的功能模块，涵盖从 API 到数据库的各个层面。

#### 5.1.1. 新增 API 端点 (Adding New API Endpoint)

当需要对外提供新的 HTTP 接口时，请遵循以下步骤：

1.  **定义 Pydantic Schema**:
    *   在 `app/api/schemas/` 目录下创建或修改相应的 `*.py` 文件。
    *   定义请求体 (Request Body) 和响应体 (Response Body) 的 Pydantic 模型。
    *   **示例**:
        ```python
        # app/api/schemas/my_feature_schemas.py
        from pydantic import BaseModel

        class MyFeatureRequest(BaseModel):
            input_data: str
            param1: int = 0

        class MyFeatureResponse(BaseModel):
            result_data: str
            status_code: int
        ```

2.  **实现 Service 逻辑**:
    *   在 `app/services/` 目录下创建或修改对应的服务文件（如 `my_feature.py`）。
    *   实现核心业务逻辑函数，该函数将由 API 端点调用。
    *   **示例**:
        ```python
        # app/services/my_feature.py
        from app.api.schemas.my_feature_schemas import MyFeatureResponse

        def process_my_feature(input_data: str, param1: int) -> MyFeatureResponse:
            # ... 业务逻辑处理 ...
            processed_result = f"Processed: {input_data} with param {param1}"
            return MyFeatureResponse(result_data=processed_result, status_code=200)
        ```

3.  **定义 API 端点**:
    *   在 `app/api/endpoints/` 目录下创建或修改对应的端点文件（如 `my_feature.py`）。
    *   创建 `APIRouter` 实例，并定义 HTTP 方法（`@router.post`, `@router.get` 等）。
    *   使用 Pydantic 模型作为请求体和响应体的类型提示。
    *   调用服务层函数，并使用 `ApiResponse` 封装响应。
    *   **示例**:
        ```python
        # app/api/endpoints/my_feature.py
        from fastapi import APIRouter, HTTPException
        from app.api.schemas.response_schemas import ApiResponse
        from app.api.schemas.my_feature_schemas import MyFeatureRequest, MyFeatureResponse
        from app.services.my_feature import process_my_feature
        from app.core.logger_config import get_logger

        logger = get_logger()
        router = APIRouter()

        @router.post("/my-feature", response_model=ApiResponse[MyFeatureResponse])
        async def create_my_feature(request: MyFeatureRequest):
            try:
                result = process_my_feature(request.input_data, request.param1)
                return ApiResponse(status='success', msg='Feature processed successfully', data=result)
            except Exception as e:
                logger.error(f"Error processing feature: {str(e)}")
                error_response = ApiResponse(status='error', msg='Processing failed', error=str(e))
                raise HTTPException(status_code=500, detail=error_response)
        ```

4.  **注册 API 路由**:
    *   在 `app/api/router.py` 中导入新定义的 `APIRouter` 实例。
    *   使用 `api_router.include_router()` 方法将其添加到主路由中，并指定 `prefix` 和 `tags`。
    *   **示例**:
        ```python
        # app/api/router.py
        from fastapi import APIRouter
        from app.api.endpoints import health, event_extraction, frontier_insights, nearby_hotspot, task_management
        from app.api.endpoints import my_feature # 导入新的路由

        api_router = APIRouter()
        api_router.include_router(health.router, tags=["Health"])
        api_router.include_router(event_extraction.router, prefix="/event-extraction", tags=["Event Extraction"])
        # ... 其他路由
        api_router.include_router(my_feature.router, prefix="/my-feature", tags=["My Feature"]) # 注册新的路由
        ```

#### 5.1.2. 新增数据库模型 (Adding New Database Model)

当需要持久化新的数据结构时，请遵循以下步骤：

1.  **定义 SQLAlchemy 模型**:
    *   在 `app/db/models.py` 中定义新的 SQLAlchemy 模型类，继承 `Base`。
    *   使用 `Column` 定义字段，指定类型、约束（如 `primary_key`, `index`, `nullable`）等。
    *   **示例**:
        ```python
        # app/db/models.py
        from sqlalchemy import Column, Integer, String, DateTime
        from sqlalchemy.sql import func
        # ... 其他导入和 Base 定义

        class MyNewData(Base):
            __tablename__ = "my_new_data"
            id = Column(Integer, primary_key=True, index=True)
            name = Column(String(255), nullable=False)
            created_at = Column(DateTime(timezone=True), server_default=func.now())

            def __repr__(self):
                return f"<MyNewData(name='{self.name}')>"
        ```

2.  **数据库迁移 (如果使用 Alembic 等)**:
    *   虽然当前项目结构中未明确包含 Alembic 等迁移工具，但在实际生产环境中，强烈建议使用数据库迁移工具来管理数据库 Schema 的变更。
    *   如果引入迁移工具，请按照其文档生成并应用新的迁移脚本。
    *   **注意**: 在开发环境中，每次启动应用时 `create_db_and_tables()` 会尝试创建表，但这不适用于生产环境的 Schema 变更管理。

3.  **在 Service 层进行数据库操作**:
    *   在对应的服务文件（如 `app/services/my_feature.py`）中，引入数据库会话。
    *   使用 SQLAlchemy ORM 进行数据的增删改查操作。
    *   **示例**:
        ```python
        # app/services/my_feature.py
        from sqlalchemy.orm import Session
        from app.db.database import get_db
        from app.db.models import MyNewData # 导入新模型

        def save_my_new_data(name: str):
            db: Session = next(get_db()) # 获取数据库会话
            new_entry = MyNewData(name=name)
            db.add(new_entry)
            db.commit()
            db.refresh(new_entry)
            return new_entry
        ```

#### 5.1.3. 新增服务 (Adding New Service)

当需要封装一组相关的业务逻辑或协调多个组件时，可以新增一个服务模块：

1.  **创建服务文件**:
    *   在 `app/services/` 目录下创建新的 Python 文件（如 `new_service.py`）。
    *   定义一个类或一组函数来封装业务逻辑。
    *   **示例**:
        ```python
        # app/services/new_service.py
        from app.core.logger_config import get_logger

        logger = get_logger()

        class NewService:
            def __init__(self):
                logger.info("NewService initialized.")

            def perform_action(self, data: str) -> str:
                logger.info(f"Performing action with: {data}")
                # ... 业务逻辑 ...
                return f"Action performed on {data}"

        # 如果服务是单例模式，可以创建全局实例
        new_service_instance = NewService()

        def perform_action_convenience(data: str) -> str:
            return new_service_instance.perform_action(data)
        ```

2.  **在其他层调用服务**:
    *   API 端点、Celery 任务或其他服务可以导入并调用新服务中定义的函数或方法。

#### 5.1.4. 新增异步任务 (Adding New Asynchronous Task)

当需要执行耗时操作且不阻塞主 Web 服务时，应将其封装为 Celery 异步任务：

1.  **实现 Service 逻辑 (如果尚未实现)**:
    *   确保异步任务所依赖的业务逻辑已在 `app/services/` 中实现。

2.  **定义 Celery Task**:
    *   在 `app/tasks/llm_tasks.py` 中定义新的 Celery 任务函数。
    *   使用 `@celery_app.task(bind=True, name="tasks.my_new_task")` 装饰器。
    *   任务函数内部调用服务层逻辑。
    *   实现任务状态更新和异步回调机制。
    *   **示例**:
        ```python
        # app/tasks/llm_tasks.py
        from datetime import datetime
        from app.tasks.celery_app import celery_app
        from app.services.new_service import perform_action_convenience # 导入新服务函数
        from app.services.schemas.callback_schemas import CallbackSuccessData, CallbackFailureData, CallbackTaskResult
        from app.api.schemas.task_schemas import TaskType, TaskStatus
        from app.core.logger_config import get_logger

        logger = get_logger()

        @celery_app.task(bind=True, name="tasks.my_new_task")
        def my_new_task(self, input_data: str, callback_url: str = None, echo: str = None) -> dict:
            try:
                task_id = self.request.id
                logger.info(f"开始执行新任务 {task_id}，输入: {input_data}")

                self.update_state(state='STARTED', meta={'task_id': task_id, 'progress': 0})

                result_data = perform_action_convenience(input_data) # 调用服务层逻辑

                # 构建结果和回调数据
                task_result = CallbackTaskResult(content=result_data, results=[], time=datetime.now().isoformat(), total=1)
                success_callback_data = CallbackSuccessData(
                    task_id=task_id, task_type=TaskType.OTHER, status=TaskStatus.SUCCESS,
                    result=task_result, submitted_at=datetime.now().isoformat(),
                    started_at=datetime.now().isoformat(), completed_at=datetime.now().isoformat(),
                    processing_time=0, echo=echo
                )

                if callback_url:
                    from app.tasks.callback_tasks import send_task_callback
                    send_task_callback.delay(callback_url, success_callback_data.model_dump())

                return {"task_id": task_id, "status": "SUCCESS", "result": result_data}

            except Exception as e:
                logger.error(f"新任务 {task_id} 执行失败: {str(e)}")
                self.update_state(state='FAILURE', meta={'task_id': task_id, 'error_message': str(e)})
                if callback_url:
                    failure_callback_data = CallbackFailureData(
                        task_id=task_id, task_type=TaskType.OTHER, status=TaskStatus.FAILURE,
                        error_message=str(e), failed_at=datetime.now().isoformat(), echo=echo
                    )
                    from app.tasks.callback_tasks import send_task_callback
                    send_task_callback.delay(callback_url, failure_callback_data.model_dump())
                raise
        ```

3.  **从 API 端点或其他地方触发任务**:
    *   在 API 端点中，通过 `.delay()` 或 `.apply_async()` 方法触发 Celery 任务。
    *   **示例**:
        ```python
        # app/api/endpoints/my_feature.py (假设需要异步处理)
        from app.tasks.llm_tasks import my_new_task

        @router.post("/my-feature-async", response_model=ApiResponse[dict])
        async def create_my_feature_async(request: MyFeatureRequest):
            task = my_new_task.delay(request.input_data, callback_url="http://your.callback.url")
            return ApiResponse(status='accepted', msg='Task submitted', data={'task_id': task.id})
        ```

#### 5.1.5. 新增 LLM 链功能 (Adding New LLM Chain Feature)

当需要添加一个全新的 LLM 链功能时，请遵循以下详细步骤：

1.  **定义 Schema**: 在 `app/llm_integrations/schemas/` 中定义新的 Pydantic 模型，用于表示 LLM 链的输入、输出和内部数据结构。
2.  **定义 Prompt**: 在 `app/llm_integrations/prompts/` 中创建新的提示词文件，定义 `map_prompt` 和 `combine_prompt` (如果需要 Map-Reduce)。
3.  **定义 Parser**: 在 `app/llm_integrations/parsers/` 中创建新的输出解析器，继承 `BaseOutputParser`，实现 `parse` 方法，将 LLM 输出解析为定义的 Schema。
4.  **定义 Config**: 在 `app/llm_integrations/configs/` 中创建新的配置类，继承 `BaseChainConfig`，并添加特定于该链的配置项。
5.  **定义 Callback**: 在 `app/llm_integrations/callbacks/` 中创建新的回调处理器，继承 `BaseChainCallback`，用于收集特定统计信息。
6.  **实现 Chain**: 在 `app/llm_integrations/chains/` 中实现新的 Chain 类，继承 `BaseChain` (如果适用)，封装 LLM 交互逻辑，集成 Prompt、Parser、Config 和 Callback。
7.  **实现 Service**: 在 `app/services/` 中实现新的服务模块，封装业务逻辑，调用新实现的 Chain。
8.  **定义 API Endpoint**: 在 `app/api/endpoints/` 中定义新的 API 端点，接收请求，调用服务层方法。
9.  **注册 API 路由**: 在 `app/api/router.py` 中注册新的 API 路由。
10. **定义 Celery Task (可选)**: 如果功能需要异步处理，在 `app/tasks/llm_tasks.py` 中定义新的 Celery 任务，调用服务层方法。
11. **编写测试**: 在 `tests/` 目录下为新功能编写单元测试和集成测试。

### 5.2. 最佳实践 (General Best Practices)

*   **代码风格与格式**:
    *   严格遵循 [PEP 8](https://peps.python.org/pep-0008/) 编码规范。
    *   推荐使用 `Black` 进行代码格式化，`isort` 进行导入排序，以保持代码风格的一致性。
*   **类型提示 (Type Hinting)**:
    *   广泛使用类型提示，尤其是在函数签名、类属性和复杂数据结构中。
    *   类型提示能够提高代码的可读性、可维护性，并有助于 IDE 进行静态分析和错误检查。
*   **日志规范**:
    *   使用 `app.core.logger_config.get_logger()` 获取日志器。
    *   根据信息的重要性选择合适的日志级别：
        *   `DEBUG`: 详细的调试信息，仅在开发或问题排查时启用。
        *   `INFO`: 重要的业务流程信息，如任务开始/结束、关键步骤完成。
        *   `WARNING`: 潜在的问题或非致命错误，不影响程序正常运行但需要关注。
        *   `ERROR`: 运行时错误，导致功能无法正常执行。
        *   `CRITICAL`: 严重错误，可能导致程序崩溃或数据丢失。
    *   日志信息应清晰、简洁，包含必要的上下文信息（如 `task_id`、输入长度等）。
*   **异常处理**:
    *   对可能发生错误的外部交互（如 LLM 调用、数据库操作、文件读写）进行适当的 `try...except` 块处理。
    *   区分预期异常（如输入验证失败、LLM 输出解析错误）和非预期异常。
    *   对于预期异常，应进行优雅降级或返回友好的错误信息。
    *   对于非预期异常，应详细记录错误日志（包括堆栈跟踪），并考虑是否需要重新抛出或转换为特定错误类型。
*   **模块化与职责分离**:
    *   严格遵循分层架构，确保每个模块和函数只负责单一职责。
    *   避免循环依赖和紧耦合，提高代码的可测试性和可维护性。
*   **安全考量**:
    *   敏感信息（如 API 密钥、数据库凭证）必须通过环境变量或 Pydantic Settings 加载，绝不能硬编码在代码中。
    *   对所有外部输入进行严格的验证和清理，防止注入攻击（如 SQL 注入、Prompt 注入）。
    *   对 LLM 的输出进行必要的审查和消毒，避免潜在的安全风险。
*   **测试**:
    *   为新功能编写单元测试和集成测试。
    *   单元测试应覆盖核心业务逻辑和边缘情况。
    *   集成测试应验证不同模块之间的交互是否正确。
    *   测试文件应放置在 `tests/` 目录下，并遵循清晰的命名约定（如 `test_module_name.py`）。
*   **文档与注释**:
    *   关键类、方法和复杂逻辑应有清晰的 Docstring，解释其功能、参数、返回值和可能抛出的异常。
    *   对于非显而易见的实现细节或设计决策，应添加行内注释。
    *   保持文档（包括本文件）与代码库的同步更新。

## 6. 部署与运行 (Deployment & Running)

*   **环境配置**: 复制 `.env.example` 为 `.env`，并根据实际环境配置各项参数。
*   **依赖安装**: 使用 `uv pip install -r requirements.txt` 安装所有依赖。
*   **启动 Web 服务**: `python run.py` (启动 FastAPI 应用)。
*   **启动 Celery Worker**: `python run_worker.py` (启动 Celery 任务消费者)。
*   **启动 Celery Flower (可选)**: `python run_flower.py` (启动 Celery 监控面板)。

## 7. 总结

LlmFlowHub 项目旨在提供一个强大、灵活且易于扩展的 LLM 集成平台。遵循上述规范和原则，将有助于我们共同构建高质量、可维护的软件系统。
