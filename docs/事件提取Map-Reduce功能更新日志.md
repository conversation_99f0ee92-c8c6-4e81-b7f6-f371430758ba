# 事件提取Map-Reduce功能更新日志 (易读版)

## 引言

本日志详细记录了“事件提取服务”中Map-Reduce功能的开发与优化过程。此功能旨在解决大型语言模型（LLM）在处理长文本时因上下文限制而导致的事件提取失败问题。通过引入Map-Reduce模式，系统能够智能地分割长文本、并行提取事件，并高效地合并与去重结果，显著提升了服务的鲁棒性和处理能力。

## 版本信息

*   **更新日期**: 2025年7月17日
*   **功能名称**: Map-Reduce事件提取
*   **影响模块**: 事件提取服务

## 更新概述

本次更新的核心是为事件提取服务引入了Map-Reduce处理模式。当输入文本长度超出LLM的上下文限制时，系统将自动启用此模式：
1.  **文本分割 (Map)**: 将长文本智能地分割成多个较小的文本块。
2.  **并行提取**: 对每个文本块独立进行事件提取。
3.  **结果合并与去重 (Reduce)**: 将所有文本块的提取结果进行智能合并，并自动识别、去除重复事件，确保最终输出的准确性和完整性。

## 主要变更详情

### 1. 配置文件的增强与统一

为了更好地支持Map-Reduce模式并提升配置的复用性，我们对相关配置文件进行了调整：

*   **`app/llm_integrations/configs/base_config.py`**:
    *   **新增** Map-Reduce模式的核心配置参数，以便其他链也能复用：
        *   `enable_map_reduce: bool = False`：是否启用Map-Reduce模式。
        *   `max_combine_attempts: int = 2`：Combine阶段的最大重试次数。
    *   **保留** 文本分块的基础配置：
        *   `chunk_size: int = 5000`：文本分块的大小。
        *   `chunk_overlap: int = 200`：文本分块的重叠区域大小。

*   **`app/llm_integrations/configs/event_extraction_config.py`**:
    *   **移除** 了原有的Map-Reduce相关配置，现在这些配置已统一到 `base_config.py` 中。

### 2. Prompt模板的优化与统一

为了适应单次处理和Map-Reduce模式的需求，我们对Prompt模板进行了优化：

*   **`app/llm_integrations/prompts/event_extraction_prompt.py`**:
    *   **新增** `event_extract_prompt`：用于单次处理模式的Prompt模板。
    *   **保留** `map_prompt`：用于Map阶段处理单个文本块的Prompt模板。
    *   **保留** `combine_prompt`：用于Combine阶段合并与去重的Prompt模板。

*   **`app/llm_integrations/prompts/__init__.py`**:
    *   **新增** 导出 `event_extract_prompt`。

### 3. 核心链功能的大幅增强

`app/llm_integrations/chains/event_extraction_chain.py` 是本次更新的核心，引入了多项新功能和方法：

*   **新增导入**:
    *   `RecursiveCharacterTextSplitter`：用于高效的文本分割。
    *   `map_prompt`, `combine_prompt`, `event_extract_prompt`：引入新的Prompt模板。

*   **新增核心方法**:
    *   `_split_text(text: str) -> List[str]`：负责将长文本分割成多个块。
    *   `_map_extract_events(text_chunk: str) -> str`：Map阶段，处理单个文本块并提取事件。
    *   `_combine_events(map_results: List[str]) -> EventExtractionResult`：Combine阶段，合并并去重Map阶段的结果。
    *   `_extract_events_single(text: str) -> EventExtractionResult`：处理短文本的单次提取模式。
    *   `_extract_events_map_reduce(text: str) -> EventExtractionResult`：处理长文本的Map-Reduce提取模式。

*   **修改主入口方法**:
    *   `extract_events(text: str) -> EventExtractionResult`：此方法现在具备智能判断能力，会根据输入文本的长度自动选择执行单次处理模式或Map-Reduce模式。

## 功能特性亮点

1.  **自动模式选择**: 系统会根据文本长度（可配置阈值）智能选择最合适的处理模式，无需手动干预。
2.  **智能文本分割**: 采用 `RecursiveCharacterTextSplitter`，支持多种中文分隔符，并可灵活配置分块大小和重叠区域，确保文本分割的准确性和连贯性。
3.  **高效Map阶段处理**: 并行处理各个文本块，显著提升处理效率；使用专门的Prompt模板确保提取质量。
4.  **智能Combine阶段处理**: 能够智能合并多个Map阶段的结果，并自动去重相似事件，同时保留信息最完整的事件记录。
5.  **健壮的错误处理与重试机制**: 内置完善的异常处理和重试逻辑，确保在遇到问题时能够优雅降级或恢复。

## 使用方式

本次更新保持了API的向后兼容性，现有调用方式无需修改。

### API 调用示例

```python
# 直接调用，系统将自动选择处理模式
result = extract_events(long_text)
```

### 服务层调用示例

```python
from app.services.event_extraction import extract_events

# 自动根据文本长度选择处理模式
result = extract_events(text)
```

### 配置调整示例

您可以通过修改配置来调整Map-Reduce的行为：

```python
from app.llm_integrations.configs.event_extraction_config import EventExtractionConfig

config = EventExtractionConfig(
    enable_map_reduce=True,           # 启用Map-Reduce模式
    map_reduce_threshold=4000,        # 触发Map-Reduce模式的文本长度阈值
    chunk_size=5000,                  # 分块大小
    chunk_overlap=200                 # 重叠大小
)
```

## 性能优化

*   **内存优化**: 分块处理有效避免了处理大文本时可能出现的内存占用过高问题。
*   **并发处理**: Map阶段支持并行处理多个文本块，大幅缩短了处理时间。
*   **智能去重**: Combine阶段的去重逻辑确保了最终结果的精简和准确。
*   **自动降级**: 在文本分割失败等异常情况下，系统能够自动回退到单次处理模式，保证服务的可用性。

## 兼容性

*   **完全向后兼容**: 现有API接口、数据格式和配置参数均保持不变，无需修改客户端代码。

## 测试建议

为了确保功能的稳定性和准确性，建议进行以下测试：

1.  **短文本测试**: 验证单次处理模式是否正常工作。
2.  **长文本测试**: 验证Map-Reduce模式是否正确触发并处理。
3.  **边界测试**: 测试文本长度在阈值附近时的行为。
4.  **错误测试**: 模拟各种异常情况，验证错误处理和重试机制。

## 注意事项

1.  **LLM调用次数增加**: Map-Reduce模式会增加对LLM的调用次数，可能影响处理速度和成本。
2.  **跨块信息丢失**: 分块处理可能导致部分跨文本块的事件关联信息丢失。
3.  **参数调整**: 建议根据实际应用场景调整阈值和分块参数，以达到最佳效果。
4.  **成本监控**: 密切监控LLM调用成本，合理设置重试次数。

## 修复与优化历史

### 2025-07-17 修复1: 单次处理模式链调用参数错误

*   **问题**: 在单次处理模式中，链调用时参数格式不正确（直接传递文本而非字典格式）。
*   **修复**: 修改 `_extract_events_single` 方法中的链调用格式，确保参数以 `{"input": text}` 的字典形式传递。
*   **验证**: 短文本和长文本处理均正常工作。

### 2025-07-17 修复2: Prompt模板使用不当

*   **问题**: 错误地创建了新的 `event_extract_prompt`，而没有统一使用现有的 `map_prompt`。
*   **修复**:
    *   删除了冗余的 `event_extract_prompt`。
    *   统一使用 `map_prompt` 作为单次处理和Map阶段的Prompt。
    *   修改链构建和调用逻辑，确保使用正确的变量名 `{text}` 而非 `{input}`。
    *   更新所有相关的导入和引用。
*   **验证**: 短文本（单次模式）和长文本（Map-Reduce模式）均正常工作。

### 2025-07-17 优化3: 智能长度判断与配置优化

*   **问题1**: 长度判断未充分考虑Prompt本身的开销，可能导致实际文本长度超出LLM上下文限制。
*   **解决方案**:
    *   新增 `_calculate_prompt_overhead()` 方法，用于精确计算Prompt的实际开销。
    *   修改长度判断逻辑：实际阈值 = `chunk_size` - Prompt开销。
    *   增加阈值有效性检查，避免Prompt开销过大导致无法处理。
*   **问题2**: Map-Reduce相关配置分散，不利于复用。
*   **解决方案**:
    *   将Map-Reduce配置（`enable_map_reduce`, `max_combine_attempts`）移动到 `base_config.py`。
    *   统一配置管理，便于其他队列复用。
*   **配置变更**:
    *   `base_config.py` 新增：`enable_map_reduce`, `max_combine_attempts`。
    *   `event_extraction_config.py` 移除：`enable_map_reduce`, `map_reduce_threshold`, `max_combine_attempts`。
*   **测试结果**:
    *   Prompt开销约为3737字符。
    *   实际有效阈值：5000 - 3737 = 1263字符。
    *   短文本（47字符）使用单次模式。
    *   长文本（4980字符）使用Map-Reduce模式。

### 2025-07-17 修复4: Prompt开销计算不准确

*   **问题**: `_calculate_prompt_overhead` 方法使用固定样本文本（“测试文本”，4字符）计算开销，而非实际输入文本。
*   **影响**: 导致Prompt开销计算不准确，可能出现错误的模式选择。
*   **修复**:
    *   修改方法签名：`_calculate_prompt_overhead(actual_text: str)`。
    *   使用实际输入文本计算Prompt开销，确保准确性。
*   **验证**: 不同长度文本的Prompt开销计算正确，模式选择准确。

### 2025-07-17 修复5: 文本分割阈值修正

*   **问题**: `_split_text` 方法使用原始的 `chunk_size` 进行分割，而不是考虑Prompt开销后的实际有效阈值。
*   **现象**: 文本长度2925字符，实际阈值1263字符，本应分成3块，但实际只分成1块。
*   **原因**: 文本分割器使用了错误的 `chunk_size` 参数。
*   **修复**:
    *   修改 `_split_text` 方法签名，接受 `actual_chunk_size` 参数。
    *   修改 `_extract_events_map_reduce` 方法，将实际有效阈值传递给分割方法。
*   **预期效果**: 2925字符的文本将被正确分割成3个块进行Map-Reduce处理。

## 后续优化方向

1.  **异步并行处理**: 支持Map阶段的异步并行处理，进一步提升效率。
2.  **分块策略优化**: 持续优化分块策略，减少因分块导致的事件信息丢失。
3.  **处理进度回调**: 增加处理进度回调功能，提供更友好的用户体验。
4.  **自定义合并策略**: 支持用户自定义合并策略，以适应更复杂的业务需求。

## 结论

本次对事件提取服务Map-Reduce功能的更新，显著提升了系统处理长文本的能力，使其在面对复杂场景时更加稳定和高效。通过智能的文本分割、并行处理和结果合并去重，我们确保了提取结果的准确性和完整性。未来的优化将进一步提升其性能和灵活性。
