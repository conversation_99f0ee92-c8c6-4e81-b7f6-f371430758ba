# 通用LLM调用方法开发计划

## 项目概述

本项目旨在实现一个新的通用LLM调用方法，其中基础提示（prompts）通过API调用从外部提供。该实现将遵循现有项目的分层架构模式，确保与代码库中用于事件提取的现有Langchain模式集成。

## 核心特性

- **动态Prompt支持**: 通过 `prompt_variables` 参数支持外部提供的 `map_prompt` 和 `combine_prompt`
- **Map-Reduce模式**: 支持长文本的分块处理和合并
- **服务层架构**: 遵循项目的API/Service/Task分层架构
- **异步任务支持**: 集成Celery异步任务处理
- **统一响应格式**: 使用项目标准的ApiResponse格式

## 已完成文件分析

### 1. general_schemas.py (LLM集成层Schema)
- 位置: `app/llm_integrations/schemas/general_schemas.py`
- 定义了 `GeneralInfoResult` 模型
- 包含基础的content和time字段

### 2. general_config.py (配置层)
- 位置: `app/llm_integrations/configs/general_config.py`
- 继承自 `BaseChainConfig`
- 提供通用配置支持

### 3. general_prompt.py (提示词层)
- 位置: `app/llm_integrations/prompts/general_prompt.py`
- 实现了动态prompt模板
- 支持 `{map_prompt}` 和 `{combine_prompt}` 变量替换

## 开发任务清单

### 阶段1: 核心组件实现

#### 1.1 通用解析器 (GeneralOutputParser) ✅ 已完成
**文件**: `app/llm_integrations/parsers/general_parser.py`
**任务**:
- ✅ 继承 `BaseOutputParser[GeneralInfoResult]`
- ✅ 实现 `parse(text: str) -> GeneralInfoResult` 方法
- ✅ 支持任意格式的LLM输出解析
- ✅ 集成现有的JSON清理和修复逻辑

**实现要点**:
- 使用LLMOutputCleaner进行基础文本清理
- 支持JSON格式检测和格式化
- 实现智能空行处理和文本规范化
- 集成JsonExtractor用于结构化数据提取
- 完整的错误处理和日志记录

**参考**: `app/llm_integrations/parsers/event_extraction_parser.py`

#### 1.2 通用回调处理器 (GeneralCallback) ✅ 已完成
**文件**: `app/llm_integrations/callbacks/general_callback.py`
**任务**:
- ✅ 继承 `BaseChainCallback`
- ✅ 实现通用的统计和监控功能
- ✅ 添加特定于通用调用的统计项

**实现要点**:
- 内容处理统计（总数、长度、平均长度）
- Map-Reduce模式使用率统计
- 内容大小分布分析（小/中/大文本）
- 处理模式统计（单次/Map-Reduce）
- 专用的统计更新方法（update_map_operation等）
- 处理摘要生成功能

**参考**: `app/llm_integrations/callbacks/event_extraction_callback.py`

#### 1.3 通用链逻辑 (GeneralChain) ✅ 已完成
**文件**: `app/llm_integrations/chains/general_chain.py`
**任务**:
- ✅ 实现完整的LLM调用链
- ✅ 支持动态prompt变量注入
- ✅ 实现Map-Reduce模式
- ✅ 集成重试机制和错误处理
- ✅ 支持prompt开销计算

**实现要点**:
- 完整的LCEL处理链构建
- 智能的单次/Map-Reduce模式选择
- 动态prompt变量支持（map_prompt和combine_prompt）
- Tenacity重试机制集成
- 文本分割和合并逻辑
- 同步和异步处理方法
- 完整的回调统计集成
- 输入验证和后处理

**参考**: `app/llm_integrations/chains/event_extraction_chain.py`

### 阶段2: 服务层实现

#### 2.1 通用服务层 (GeneralService) ✅ 已完成
**文件**: `app/services/general.py`
**任务**:
- ✅ 封装GeneralChain的调用逻辑
- ✅ 提供简洁的服务接口
- ✅ 实现文本清理和预处理
- ✅ 支持prompt_variables参数传递

**实现要点**:
- 懒加载GeneralChain实例
- 完整的prompt_variables验证
- 同步和异步处理方法
- 统计信息获取和重置功能
- prompt格式验证和格式化
- 全局服务实例和便捷函数
- 完整的错误处理和日志记录

**参考**: `app/services/event_extraction.py`

### 阶段3: API层实现

#### 3.1 API Schema模型 ✅ 已完成
**文件**: `app/api/schemas/general_schemas.py`
**任务**:
- ✅ 定义 `GeneralRequest` 模型（包含content和prompt_variables）
- ✅ 定义 `GeneralResponse` 模型
- ✅ 定义 `GeneralResultResponse` 模型
- ✅ 实现 `from_source_result` 类方法

**实现要点**:
- 完整的请求/响应模型定义
- 支持批量处理的模型（GeneralBatchRequest/Response）
- 统计信息响应模型（GeneralStatsResponse）
- 详细的字段描述和验证
- 示例配置和文档
- 处理信息集成和转换方法

**参考**: `app/api/schemas/event_extraction_schemas.py`

#### 3.2 API端点实现 ✅ 已完成
**文件**: `app/api/endpoints/general/general.py`
**任务**:
- ✅ 创建APIRouter实例
- ✅ 实现POST端点接收通用LLM调用请求
- ✅ 集成错误处理和日志记录
- ✅ 使用统一的ApiResponse格式

**实现要点**:
- 主要处理端点（POST /）
- 批量处理端点（POST /batch）
- 统计信息端点（GET /stats, POST /stats/reset）
- prompt验证端点（POST /validate-prompt）
- 完整的错误处理和HTTP状态码
- 详细的日志记录和请求追踪
- 统一的ApiResponse格式

**参考**: `app/api/endpoints/event_extraction/event_extraction.py`

#### 3.3 路由注册 ✅ 已完成
**文件**: `app/api/router.py`
**任务**:
- ✅ 导入general路由
- ✅ 注册到主路由器
- ✅ 设置适当的前缀和标签

**实现要点**:
- 导入 `from app.api.endpoints.general import general`
- 注册路由：`prefix='/api/v1/general', tags=["通用LLM调用"]`
- 保持与其他路由的一致性

### 阶段4: 异步任务支持

#### 4.1 Celery任务实现 ✅ 已完成
**文件**: `app/tasks/llm_tasks.py`
**任务**:
- ✅ 添加 `general_llm_task` 函数
- ✅ 支持回调机制
- ✅ 实现任务状态更新
- ✅ 集成错误处理

**实现要点**:
- 完整的Celery任务装饰器配置
- 任务状态管理（STARTED/SUCCESS/FAILURE）
- 标准化的回调数据结构
- 处理时间统计和日志记录
- 错误处理和失败回调
- 与现有任务模式保持一致

**参考**: 现有的事件提取任务实现

### 阶段5: 测试和文档

#### 5.1 单元测试
**文件**: `tests/test_general_*.py`
**任务**:
- 测试解析器功能
- 测试链逻辑
- 测试服务层
- 测试API端点
- 测试异步任务

#### 5.2 文档更新 ✅ 已完成
**文件**: `README.md`, `docs/`
**任务**:
- ✅ 添加通用LLM调用的API文档
- ✅ 更新使用示例
- ✅ 添加配置说明

**实现要点**:
- 在README.md中添加完整的通用LLM调用接口文档
- 包含所有端点的详细说明（/, /batch, /stats, /validate-prompt）
- 提供完整的请求/响应示例
- 更新核心功能描述
- 调整章节编号保持文档结构一致

## 技术要点

### 动态Prompt处理
```python
# 在GeneralChain中实现
def _prepare_prompt_variables(self, base_variables: Dict[str, Any], 
                            prompt_variables: Optional[Dict[str, Any]] = None) -> Dict[str, Any]:
    """合并基础变量和动态prompt变量"""
    final_variables = base_variables.copy()
    if prompt_variables:
        final_variables.update(prompt_variables)
    return final_variables
```

### Map-Reduce模式支持
- 使用 `general_prompt.py` 中定义的 `map_prompt` 和 `combine_prompt`
- 通过prompt_variables动态注入具体的提示内容
- 支持长文本分块处理和结果合并

### API请求格式
```json
{
  "content": "需要处理的文本内容",
  "prompt_variables": {
    "map_prompt": "具体的map阶段提示词",
    "combine_prompt": "具体的combine阶段提示词"
  }
}
```

## 开发优先级

1. **高优先级**: 核心组件实现（解析器、链逻辑、服务层）
2. **中优先级**: API层实现和路由注册
3. **低优先级**: 异步任务支持、测试和文档

## 质量保证

- 遵循项目的编码规范和架构模式
- 确保与现有LLM集成的一致性
- 实现完整的错误处理和日志记录
- 编写全面的单元测试
- 保持API设计的一致性

## 项目完成状态

### ✅ 已完成的任务

1. **通用解析器 (GeneralOutputParser)** - 完成
2. **通用回调处理器 (GeneralCallback)** - 完成
3. **通用链逻辑 (GeneralChain)** - 完成
4. **通用服务层 (GeneralService)** - 完成
5. **API Schema模型** - 完成
6. **API端点实现** - 完成
7. **路由注册** - 完成
8. **异步任务支持** - 完成
9. **项目文档更新** - 完成

### ❌ 已取消的任务

1. **单元测试** - 按用户要求取消，由用户自行测试

## 实现总结

通用LLM调用方法已完全实现，包含以下核心特性：

- **完整的分层架构**: 遵循项目的API/Service/LLM集成/Task分层模式
- **动态Prompt支持**: 支持通过prompt_variables动态注入map_prompt和combine_prompt
- **Map-Reduce模式**: 自动处理长文本的分块和合并
- **多种API端点**: 单次处理、批量处理、统计信息、prompt验证
- **异步任务支持**: 完整的Celery任务集成和回调机制
- **统一响应格式**: 与现有API保持一致的响应结构
- **完整文档**: 详细的API文档和使用示例

## 使用方式

### 基本调用
```bash
curl -X POST "http://localhost:8000/api/v1/general/" \
  -H "Content-Type: application/json" \
  -d '{
    "content": "需要处理的文本内容",
    "prompt_variables": {
      "map_prompt": "请分析以下文本：{text}",
      "combine_prompt": "请整合以下结果：{text}"
    }
  }'
```

### 批量处理
```bash
curl -X POST "http://localhost:8000/api/v1/general/batch" \
  -H "Content-Type: application/json" \
  -d '{
    "items": [
      {
        "content": "第一个文本",
        "prompt_variables": {
          "map_prompt": "分析：{text}",
          "combine_prompt": "整合：{text}"
        }
      }
    ]
  }'
```

### 获取统计信息
```bash
curl -X GET "http://localhost:8000/api/v1/general/stats"
```

## 项目已就绪

通用LLM调用方法的实现已完成，可以开始使用和测试。所有组件都已集成到现有的项目架构中，保持了代码的一致性和可维护性。
