# 指南：通过 API 动态化 Prompt 变量

## 1. 功能概述

为了提高系统的灵活性和适应性，我们对核心的提取链（`FrontierInsightsChain`, `EventExtractionChain`, `NearbyHotspotChain`）进行了一次重要的重构。现在，您可以在调用其 API 时，通过一个名为 `prompt_variables` 的额外参数，动态地向 Prompt 模板中注入自定义的键值对。

这个功能使得我们可以在不修改任何底层代码的情况下，快速调整和实验不同的 Prompt 指令，例如更改输出语言、调整提取任务的上下文、或者为特定场景提供额外示例。

## 2. 技术实现

我们对系统进行了以下三层改造：

1.  **API Schema 层**：
    *   在 `FrontierInsightsRequest`, `EventExtractionRequest`, `NearbyHotspotRequest` 以及异步任务的 `TaskSubmitRequest` 模型中，增加了一个可选字段 `prompt_variables: Optional[Dict[str, Any]]`。
    *   这允许 API 的调用者传入一个 JSON 对象，其中包含了希望注入到 Prompt 中的变量。

2.  **服务与任务层 (Service & Tasks)**：
    *   相应地，`*Service` 中的核心方法和 `llm_tasks.py` 中的 Celery 任务函数现在都能接收并传递 `prompt_variables` 参数。

3.  **核心链层 (Chain)**：
    *   所有核心 Chain 的 `extract_*` 方法都已更新，可以接收 `prompt_variables` 字典。
    *   在执行时，Chain 会将这个字典中的键值对与默认的 Prompt 变量（如 `text`）合并。
    *   最终，LangChain 的 `PromptTemplate` 会使用这个合并后的完整变量集来格式化最终发送给大模型的 Prompt。

## 3. 重要修复说明 (2025-07-23)

我们修复了一个关于 `prompt_variables` 在 Map-Reduce 模式下长度计算不准确的关键 Bug。

-   **问题描述**: 此前，用于判断是否启用 Map-Reduce 的 `_calculate_prompt_overhead` 方法，在计算 Prompt 开销时，没有将动态传入的 `prompt_variables` 的长度计算在内。这会导致当 `prompt_variables` 包含较长文本时，系统错误地低估了总的 Prompt 长度，可能导致本应被分割的长文本被当作单块文本处理，从而超出大模型的上下文限制并引发错误。
-   **修复措施**: 我们已经重构了所有核心 Chain 中的 `_calculate_prompt_overhead` 方法。现在，它会正确地接收 `prompt_variables` 参数，并将其与所有其他变量一同用于格式化 Prompt，从而实现对总长度的精确计算。这确保了 Map-Reduce 模式的触发条件现在是完全准确的。

## 4. 如何使用

### 同步直接调用

当您向以下任意一个 API 端点发送 POST 请求时，只需在 JSON 请求体中加入 `prompt_variables` 字段即可。

-   `/api/frontier-insights/`
-   `/api/event-extraction/`
-   `/api/nearby-hotspot/`

**请求示例 (Request Body):**

```json
{
  "content": "...这里是需要分析的大段文本...",
  "prompt_variables": {
    "language": "英文"
  }
}
```

在这个例子中，`FrontierInsightsChain` 在运行时会将 `{"language": "英文"}` 这个键值对应用到 Prompt 模板中，使得大模型被明确指示以英文进行输出，而无需修改任何代码。

### 异步任务调用 (队列)

本功能同样无缝支持通过 Celery 队列执行的异步任务。当您向 `/api/tasks/submit` 端点提交一个新任务时，可以在请求体中包含 `prompt_variables` 字段。

**请求示例 (Request Body for `/api/tasks/submit`):**

```json
{
  "task_type": "frontier_insights",
  "content": "...这里是需要异步分析的大段文本...",
  "callback_url": "https://your-callback-url.com/hook",
  "echo": "some_unique_id_123",
  "prompt_variables": {
    "language": "日文"
  }
}
```

在这个例子中，任务提交后，Celery Worker 在执行 `frontier_insights_task` 时，会将 `{"language": "日文"}` 这个变量传递给核心的提取链，从而实现与同步调用完全一致的动态 Prompt 功能。

如果 `prompt_variables` 未被提供，系统将平稳地使用模板中定义的默认变量，不会造成任何影响。

## 5. 总结

通过这次重构和修复，我们不仅极大地增强了系统的动态配置能力，还确保了其在处理长文本时的稳定性和准确性，为未来的快速迭代和优化奠定了坚实的基础。