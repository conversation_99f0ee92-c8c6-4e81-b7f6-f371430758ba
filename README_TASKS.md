# LLMFlowHub 任务管理接口详细文档

## 概述

LLMFlowHub 提供完整的异步任务管理系统，基于 Celery + Redis 架构，支持事件提取、前沿动态提取、周边热点事件提取等多种LLM处理任务的异步执行。

## 技术架构

- **任务队列**: Celery
- **消息代理**: Redis
- **结果存储**: Redis
- **任务监控**: Flower

## 任务类型

| 任务类型 | 枚举值 | 描述 | 对应同步接口 |
|---------|--------|------|-------------|
| 事件提取 | `event_extraction` | 从文本中提取事件信息 | `/api/v1/event-extraction/` |
| 前沿动态提取 | `frontier_insights` | 提取前沿技术动态 | `/api/v1/frontier-insights/` |
| 周边热点事件提取 | `nearby_hotspot` | 提取周边热点事件 | `/api/v1/nearby-hotspot/` |

## 任务状态

| 状态 | 枚举值 | 描述 | 说明 |
|------|--------|------|------|
| 等待中 | `PENDING` | 任务已提交，等待Worker处理 | 初始状态 |
| 已开始 | `STARTED` | Worker已开始处理任务 | 执行中 |
| 成功 | `SUCCESS` | 任务执行成功 | 最终状态 |
| 失败 | `FAILURE` | 任务执行失败 | 最终状态 |
| 重试中 | `RETRY` | 任务正在重试 | 中间状态 |
| 已撤销 | `REVOKED` | 任务被用户或系统取消 | 最终状态 |

---

## 1. 任务提交接口

### POST /api/v1/tasks/submit

提交异步处理任务到队列。

#### 请求参数

```json
{
  "task_type": "event_extraction",
  "content": "昨天下午，苹果公司在加州总部发布了最新款iPhone 15系列产品，该产品采用了全新的A17芯片技术。",
  "callback_url": "https://your-domain.com/api/callback/task-complete",
  "echo": "batch_001_task_123",
  "prompt_variables": {
    "language": "中文",
    "domain": "科技数码"
  }
}
```

#### 请求字段详细说明

| 字段 | 类型 | 必需 | 默认值 | 验证规则 | 描述 |
|------|------|------|--------|----------|------|
| `task_type` | string | ✅ | - | 枚举值 | 任务类型，必须是以下之一：<br/>• `event_extraction`<br/>• `frontier_insights`<br/>• `nearby_hotspot` |
| `content` | string | ✅ | - | 1-50000字符 | 需要处理的文本内容，不能为空 |
| `callback_url` | string | ❌ | null | URL格式 | 任务完成后的HTTP回调地址，支持占位符：<br/>• `{task_id}`: 任务ID<br/>• `{status}`: 任务状态 |
| `echo` | string | ❌ | null | 无限制 | 用户自定义回显参数，会在所有响应中原样返回，用于关联业务数据 |
| `prompt_variables` | object | ❌ | null | 键值对 | 用于动态格式化Prompt的额外变量。例如 `{"language": "英文"}`。 |

#### 响应示例

**成功响应**:
```json
{
  "status": "success",
  "msg": "任务提交成功",
  "data": {
    "task_id": "a1b2c3d4-e5f6-7890-abcd-ef1234567890",
    "task_type": "event_extraction",
    "status": "PENDING",
    "submitted_at": "2025-07-18T14:30:00.123456",
    "estimated_completion_time": null,
    "echo": "batch_001_task_123"
  }
}
```

**错误响应**:
```json
{
  "status": "error",
  "msg": "任务提交失败",
  "error": "不支持的任务类型: invalid_type"
}
```

#### 响应字段说明

| 字段 | 类型 | 描述 |
|------|------|------|
| `task_id` | string | 任务唯一标识符（UUID格式） |
| `task_type` | string | 任务类型（与请求一致） |
| `status` | string | 任务状态（提交时总是PENDING） |
| `submitted_at` | string | 任务提交时间（ISO 8601格式） |
| `estimated_completion_time` | string/null | 预计完成时间（当前版本为null） |
| `echo` | string/null | 回显参数（与请求一致） |

---

## 2. 任务结果查询接口

### GET /api/v1/tasks/result/{task_id}

获取任务的执行结果和状态信息（合并接口，同时返回状态和结果）。

#### 路径参数

| 参数 | 类型 | 必需 | 描述 |
|------|------|------|------|
| `task_id` | string | ✅ | 任务ID（UUID格式） |

#### 请求示例

```bash
GET /api/v1/tasks/result/a1b2c3d4-e5f6-7890-abcd-ef1234567890
```

#### 响应示例

**任务成功完成**:
```json
{
  "status": "success",
  "msg": "获取任务信息成功",
  "data": {
    "task_id": "a1b2c3d4-e5f6-7890-abcd-ef1234567890",
    "task_type": "event_extraction",
    "status": "SUCCESS",
    "result": {
      "content": "昨天下午，苹果公司在加州总部发布了最新款iPhone 15系列产品。",
      "events": [
        {
          "type": "产品行为-发布",
          "trigger": "发布",
          "description": "苹果公司发布iPhone 15系列产品",
          "arguments": [
            {
              "role": "施事者",
              "values": ["苹果公司"]
            },
            {
              "role": "受事者",
              "values": ["iPhone 15系列产品"]
            },
            {
              "role": "时间",
              "values": ["昨天下午"]
            },
            {
              "role": "地点",
              "values": ["加州总部"]
            }
          ]
        }
      ],
      "time": "2025-07-18 14:32:15",
      "total": 1
    },
    "submitted_at": "2025-07-18T14:30:00.123456",
    "started_at": "2025-07-18T14:30:05.678901",
    "completed_at": "2025-07-18T14:32:15.234567",
    "processing_time": 129.56,
    "error_message": null,
    "progress": 100.0,
    "retry_count": 0,
    "echo": "batch_001_task_123"
  }
}
```

**任务执行中**:
```json
{
  "status": "success",
  "msg": "获取任务信息成功",
  "data": {
    "task_id": "a1b2c3d4-e5f6-7890-abcd-ef1234567890",
    "task_type": "event_extraction",
    "status": "STARTED",
    "result": null,
    "submitted_at": "2025-07-18T14:30:00.123456",
    "started_at": "2025-07-18T14:30:05.678901",
    "completed_at": null,
    "processing_time": null,
    "error_message": null,
    "progress": 65.0,
    "retry_count": 0,
    "echo": "batch_001_task_123"
  }
}
```

**任务失败**:
```json
{
  "status": "success",
  "msg": "获取任务信息成功",
  "data": {
    "task_id": "a1b2c3d4-e5f6-7890-abcd-ef1234567890",
    "task_type": "event_extraction",
    "status": "FAILURE",
    "result": null,
    "submitted_at": "2025-07-18T14:30:00.123456",
    "started_at": "2025-07-18T14:30:05.678901",
    "completed_at": "2025-07-18T14:31:20.345678",
    "processing_time": 74.67,
    "error_message": "LLM API连接超时",
    "progress": null,
    "retry_count": 2,
    "echo": "batch_001_task_123"
  }
}
```

#### 响应字段详细说明

| 字段 | 类型 | 可选 | 描述 |
|------|------|------|------|
| `task_id` | string | ❌ | 任务ID |
| `task_type` | string | ❌ | 任务类型 |
| `status` | string | ❌ | 当前任务状态 |
| `result` | object/null | ✅ | 任务结果数据（仅SUCCESS状态时有值） |
| `submitted_at` | string | ❌ | 任务提交时间 |
| `started_at` | string/null | ✅ | 任务开始执行时间 |
| `completed_at` | string/null | ✅ | 任务完成时间（成功或失败） |
| `processing_time` | number/null | ✅ | 实际处理耗时（秒，精确到毫秒） |
| `error_message` | string/null | ✅ | 错误信息（仅FAILURE状态时有值） |
| `progress` | number/null | ✅ | 任务进度百分比（0-100） |
| `retry_count` | integer | ❌ | 已重试次数 |
| `echo` | string/null | ✅ | 用户回显参数 |

---

## 3. 任务列表查询接口

### GET /api/v1/tasks/list

获取任务列表，支持多种过滤条件和分页。

#### 查询参数

| 参数 | 类型 | 必需 | 默认值 | 验证规则 | 描述 |
|------|------|------|--------|----------|------|
| `task_type` | string | ❌ | null | 枚举值 | 按任务类型过滤 |
| `status` | string | ❌ | null | 枚举值 | 按任务状态过滤 |
| `limit` | integer | ❌ | 20 | 1-100 | 返回数量限制 |
| `offset` | integer | ❌ | 0 | ≥0 | 分页偏移量 |

#### 请求示例

```bash
# 获取所有任务（默认分页）
GET /api/v1/tasks/list

# 获取事件提取类型的成功任务
GET /api/v1/tasks/list?task_type=event_extraction&status=SUCCESS

# 分页获取任务（第2页，每页10条）
GET /api/v1/tasks/list?limit=10&offset=10

# 组合查询
GET /api/v1/tasks/list?task_type=frontier_insights&status=PENDING&limit=5&offset=0
```

#### 响应示例

```json
{
  "status": "success",
  "msg": "获取任务列表成功",
  "data": {
    "tasks": [
      {
        "task_id": "a1b2c3d4-e5f6-7890-abcd-ef1234567890",
        "task_type": "event_extraction",
        "status": "SUCCESS",
        "progress": 100.0,
        "submitted_at": "2025-07-18T14:30:00.123456",
        "started_at": "2025-07-18T14:30:05.678901",
        "completed_at": "2025-07-18T14:32:15.234567",
        "error_message": null,
        "retry_count": 0,
        "echo": "batch_001_task_123"
      },
      {
        "task_id": "b2c3d4e5-f6g7-8901-bcde-f23456789012",
        "task_type": "frontier_insights",
        "status": "STARTED",
        "progress": 45.0,
        "submitted_at": "2025-07-18T14:35:00.456789",
        "started_at": "2025-07-18T14:35:03.123456",
        "completed_at": null,
        "error_message": null,
        "retry_count": 0,
        "echo": "batch_002_task_456"
      }
    ],
    "total": 25,
    "limit": 20,
    "offset": 0
  }
}
```

#### 响应字段说明

| 字段 | 类型 | 描述 |
|------|------|------|
| `tasks` | array | 任务状态信息列表 |
| `total` | integer | 符合条件的任务总数 |
| `limit` | integer | 当前查询的数量限制 |
| `offset` | integer | 当前查询的偏移量 |

**任务对象字段**（与结果查询接口基本一致，但不包含result字段）:
- `task_id`: 任务ID
- `task_type`: 任务类型
- `status`: 任务状态
- `progress`: 任务进度
- `submitted_at`: 提交时间
- `started_at`: 开始时间
- `completed_at`: 完成时间
- `error_message`: 错误信息
- `retry_count`: 重试次数
- `echo`: 回显参数

---

## 4. 任务取消接口

### POST /api/v1/tasks/cancel

取消指定的任务（仅对PENDING和STARTED状态的任务有效）。

#### 请求参数

```json
{
  "task_id": "a1b2c3d4-e5f6-7890-abcd-ef1234567890",
  "reason": "用户主动取消任务"
}
```

#### 请求字段说明

| 字段 | 类型 | 必需 | 描述 |
|------|------|------|------|
| `task_id` | string | ✅ | 要取消的任务ID |
| `reason` | string | ❌ | 取消原因（用于日志记录） |

#### 响应示例

**成功取消**:
```json
{
  "status": "success",
  "msg": "任务取消成功",
  "data": {
    "task_id": "a1b2c3d4-e5f6-7890-abcd-ef1234567890",
    "status": "REVOKED",
    "cancelled_at": "2025-07-18T14:35:00.789012",
    "reason": "用户主动取消任务"
  }
}
```

**取消失败**:
```json
{
  "status": "error",
  "msg": "取消任务失败",
  "error": "任务已完成，无法取消"
}
```

#### 响应字段说明

| 字段 | 类型 | 描述 |
|------|------|------|
| `task_id` | string | 任务ID |
| `status` | string | 任务状态（REVOKED） |
| `cancelled_at` | string | 取消时间 |
| `reason` | string/null | 取消原因 |

---

## 5. Redis健康检查接口

### GET /api/v1/tasks/health/redis

检查Redis连接状态，确保任务队列系统正常运行。

#### 请求参数

无

#### 响应示例

**连接正常**:
```json
{
  "status": "success",
  "msg": "Redis连接正常",
  "data": {
    "status": "success",
    "broker_connection": true,
    "result_backend_connection": true,
    "message": "所有Redis连接正常",
    "broker_info": {
      "host": "localhost",
      "port": 6379,
      "db": 0
    },
    "backend_info": {
      "host": "localhost", 
      "port": 6379,
      "db": 0
    }
  }
}
```

**连接异常**:
```json
{
  "status": "error",
  "msg": "Redis连接异常",
  "data": {
    "status": "error",
    "broker_connection": false,
    "result_backend_connection": true,
    "message": "Broker连接失败",
    "error_details": "Connection refused"
  }
}
```

---

## 6. 队列健康检查接口

### GET /api/v1/tasks/health/queue

检查整个任务队列系统的健康状态，包括Redis、Worker、队列信息等。

#### 请求参数

无

#### 响应示例

**系统正常**:
```json
{
  "status": "success",
  "msg": "队列状态检查完成",
  "data": {
    "redis_status": {
      "status": "success",
      "broker_connection": true,
      "result_backend_connection": true,
      "message": "所有Redis连接正常"
    },
    "queue_info": {
      "status": "success",
      "queues": {
        "celery": 0,
        "priority.high": 2,
        "priority.normal": 5,
        "priority.low": 1
      },
      "total_pending": 8
    },
    "worker_stats": {
      "worker1@hostname": {
        "status": "online",
        "active": 1,
        "processed": 156,
        "load": [0.1, 0.2, 0.15]
      }
    },
    "active_tasks": {
      "worker1@hostname": [
        {
          "id": "a1b2c3d4-e5f6-7890-abcd-ef1234567890",
          "name": "app.tasks.llm_tasks.event_extraction_task",
          "args": ["文本内容", null, "echo_value"],
          "kwargs": {},
          "time_start": 1642518600.123
        }
      ]
    }
  }
}
```

#### 响应字段说明

| 字段 | 类型 | 描述 |
|------|------|------|
| `redis_status` | object | Redis连接状态 |
| `queue_info` | object | 队列信息和待处理任务数 |
| `worker_stats` | object | Worker状态统计 |
| `active_tasks` | object | 当前正在执行的任务 |

---

## 回调机制

当任务完成时（成功或失败），系统会向指定的callback_url发送HTTP POST请求。

### 回调请求格式

```json
{
  "task_id": "a1b2c3d4-e5f6-7890-abcd-ef1234567890",
  "task_type": "event_extraction",
  "status": "SUCCESS",
  "result": {
    // 任务结果数据
  },
  "completed_at": "2025-07-18T14:32:15.234567",
  "processing_time": 129.56,
  "echo": "batch_001_task_123"
}
```

---

## 最佳实践

### 1. 任务提交
- 合理设置优先级，避免所有任务都使用高优先级
- 使用echo参数关联业务数据
- 设置合适的callback_url进行异步通知

### 2. 结果查询
- 对于长时间运行的任务，建议使用轮询方式查询结果
- 轮询间隔建议：5-10秒
- 设置合理的超时时间

### 3. 错误处理
- 监控任务失败率，及时处理异常
- 对于重要任务，考虑实现重试机制
- 记录详细的错误日志

### 4. 性能优化
- 批量提交任务时，合理控制并发数
- 监控队列长度，避免积压过多任务
- 根据业务需求调整Worker数量

---

## 常见问题

### Q: 任务提交后多久开始执行？
A: 取决于当前队列长度和Worker数量，通常在几秒内开始执行。

### Q: 任务结果保存多长时间？
A: 默认保存24小时，可通过配置调整。

### Q: 如何处理任务超时？
A: 系统会自动重试失败的任务，最多重试3次。

### Q: 可以修改已提交的任务吗？
A: 不支持修改，只能取消后重新提交。

### Q: 如何监控任务执行情况？
A: 可以使用Flower监控界面或调用健康检查接口。

---

## 代码示例

### Python完整示例

```python
import requests
import time
import json

class LLMFlowHubTaskClient:
    def __init__(self, base_url="http://localhost:8000"):
        self.base_url = base_url

    def submit_task(self, task_type, content, priority=5, callback_url=None, echo=None):
        """提交任务"""
        url = f"{self.base_url}/api/v1/tasks/submit"
        payload = {
            "task_type": task_type,
            "content": content,
            "priority": priority
        }
        if callback_url:
            payload["callback_url"] = callback_url
        if echo:
            payload["echo"] = echo

        response = requests.post(url, json=payload)
        return response.json()

    def get_task_result(self, task_id):
        """获取任务结果"""
        url = f"{self.base_url}/api/v1/tasks/result/{task_id}"
        response = requests.get(url)
        return response.json()

    def wait_for_completion(self, task_id, timeout=300, poll_interval=5):
        """等待任务完成"""
        start_time = time.time()
        while time.time() - start_time < timeout:
            result = self.get_task_result(task_id)
            if result["status"] == "success":
                status = result["data"]["status"]
                if status in ["SUCCESS", "FAILURE", "REVOKED"]:
                    return result
                print(f"任务状态: {status}, 进度: {result['data'].get('progress', 0)}%")
            time.sleep(poll_interval)
        raise TimeoutError(f"任务 {task_id} 在 {timeout} 秒内未完成")

    def cancel_task(self, task_id, reason=None):
        """取消任务"""
        url = f"{self.base_url}/api/v1/tasks/cancel"
        payload = {"task_id": task_id}
        if reason:
            payload["reason"] = reason
        response = requests.post(url, json=payload)
        return response.json()

    def list_tasks(self, task_type=None, status=None, limit=20, offset=0):
        """获取任务列表"""
        url = f"{self.base_url}/api/v1/tasks/list"
        params = {"limit": limit, "offset": offset}
        if task_type:
            params["task_type"] = task_type
        if status:
            params["status"] = status
        response = requests.get(url, params=params)
        return response.json()

# 使用示例
if __name__ == "__main__":
    client = LLMFlowHubTaskClient()

    # 1. 提交事件提取任务
    print("提交任务...")
    result = client.submit_task(
        task_type="event_extraction",
        content="昨天下午，苹果公司在加州总部发布了最新款iPhone 15系列产品。",
        priority=3,
        echo="demo_task_001"
    )

    if result["status"] == "success":
        task_id = result["data"]["task_id"]
        print(f"任务提交成功，ID: {task_id}")

        # 2. 等待任务完成
        try:
            final_result = client.wait_for_completion(task_id)
            print("任务完成！")
            print(json.dumps(final_result, indent=2, ensure_ascii=False))
        except TimeoutError as e:
            print(f"任务超时: {e}")
            # 取消超时任务
            cancel_result = client.cancel_task(task_id, "任务超时")
            print(f"任务已取消: {cancel_result}")
    else:
        print(f"任务提交失败: {result}")
```

### JavaScript/Node.js示例

```javascript
const axios = require('axios');

class LLMFlowHubTaskClient {
    constructor(baseUrl = 'http://localhost:8000') {
        this.baseUrl = baseUrl;
    }

    async submitTask(taskType, content, options = {}) {
        const payload = {
            task_type: taskType,
            content: content,
            priority: options.priority || 5
        };

        if (options.callbackUrl) payload.callback_url = options.callbackUrl;
        if (options.echo) payload.echo = options.echo;

        try {
            const response = await axios.post(`${this.baseUrl}/api/v1/tasks/submit`, payload);
            return response.data;
        } catch (error) {
            throw new Error(`任务提交失败: ${error.response?.data?.error || error.message}`);
        }
    }

    async getTaskResult(taskId) {
        try {
            const response = await axios.get(`${this.baseUrl}/api/v1/tasks/result/${taskId}`);
            return response.data;
        } catch (error) {
            throw new Error(`获取任务结果失败: ${error.response?.data?.error || error.message}`);
        }
    }

    async waitForCompletion(taskId, timeout = 300000, pollInterval = 5000) {
        const startTime = Date.now();

        while (Date.now() - startTime < timeout) {
            const result = await this.getTaskResult(taskId);

            if (result.status === 'success') {
                const status = result.data.status;
                if (['SUCCESS', 'FAILURE', 'REVOKED'].includes(status)) {
                    return result;
                }
                console.log(`任务状态: ${status}, 进度: ${result.data.progress || 0}%`);
            }

            await new Promise(resolve => setTimeout(resolve, pollInterval));
        }

        throw new Error(`任务 ${taskId} 在 ${timeout}ms 内未完成`);
    }

    async cancelTask(taskId, reason = null) {
        const payload = { task_id: taskId };
        if (reason) payload.reason = reason;

        try {
            const response = await axios.post(`${this.baseUrl}/api/v1/tasks/cancel`, payload);
            return response.data;
        } catch (error) {
            throw new Error(`取消任务失败: ${error.response?.data?.error || error.message}`);
        }
    }
}

// 使用示例
async function main() {
    const client = new LLMFlowHubTaskClient();

    try {
        // 提交任务
        console.log('提交任务...');
        const submitResult = await client.submitTask(
            'event_extraction',
            '昨天下午，苹果公司在加州总部发布了最新款iPhone 15系列产品。',
            {
                priority: 3,
                echo: 'demo_task_001'
            }
        );

        const taskId = submitResult.data.task_id;
        console.log(`任务提交成功，ID: ${taskId}`);

        // 等待完成
        const finalResult = await client.waitForCompletion(taskId);
        console.log('任务完成！');
        console.log(JSON.stringify(finalResult, null, 2));

    } catch (error) {
        console.error('错误:', error.message);
    }
}

main();
```

### cURL脚本示例

```bash
#!/bin/bash

BASE_URL="http://localhost:8000"

# 1. 提交任务
echo "提交任务..."
SUBMIT_RESPONSE=$(curl -s -X POST "${BASE_URL}/api/v1/tasks/submit" \
  -H "Content-Type: application/json" \
  -d '{
    "task_type": "event_extraction",
    "content": "昨天下午，苹果公司在加州总部发布了最新款iPhone 15系列产品。",
    "priority": 3,
    "echo": "bash_demo_001"
  }')

echo "提交响应: $SUBMIT_RESPONSE"

# 提取任务ID
TASK_ID=$(echo $SUBMIT_RESPONSE | jq -r '.data.task_id')
echo "任务ID: $TASK_ID"

# 2. 轮询任务状态
echo "等待任务完成..."
while true; do
  RESULT_RESPONSE=$(curl -s -X GET "${BASE_URL}/api/v1/tasks/result/${TASK_ID}")
  STATUS=$(echo $RESULT_RESPONSE | jq -r '.data.status')
  PROGRESS=$(echo $RESULT_RESPONSE | jq -r '.data.progress // 0')

  echo "任务状态: $STATUS, 进度: $PROGRESS%"

  if [[ "$STATUS" == "SUCCESS" || "$STATUS" == "FAILURE" || "$STATUS" == "REVOKED" ]]; then
    echo "任务完成！"
    echo "最终结果: $RESULT_RESPONSE" | jq '.'
    break
  fi

  sleep 5
done

# 3. 获取任务列表
echo "获取任务列表..."
curl -s -X GET "${BASE_URL}/api/v1/tasks/list?limit=5" | jq '.'
```

---

## 监控和运维

### Flower监控界面

启动Flower监控服务：
```bash
python run_flower.py
```

访问监控界面：http://localhost:5555

### 关键监控指标

1. **队列长度**: 监控待处理任务数量
2. **Worker状态**: 确保Worker正常运行
3. **任务成功率**: 监控任务失败率
4. **处理时间**: 监控任务平均处理时间
5. **Redis连接**: 确保Redis服务正常

### 日志监控

重要日志文件：
- `logs/app.log`: API服务日志
- `logs/worker.log`: Worker执行日志

关键日志关键词：
- `任务提交成功`: 任务提交
- `任务执行完成`: 任务完成
- `任务执行失败`: 任务失败
- `Redis连接异常`: Redis问题

---

## 故障排除

### 常见问题及解决方案

#### 1. 任务一直处于PENDING状态
**原因**: Worker未启动或连接问题
**解决**:
- 检查Worker进程是否运行
- 检查Redis连接
- 查看Worker日志

#### 2. 任务执行失败率高
**原因**: LLM API问题或网络问题
**解决**:
- 检查LLM API服务状态
- 检查网络连接
- 调整超时设置

#### 3. 队列积压严重
**原因**: Worker处理能力不足
**解决**:
- 增加Worker进程数
- 优化任务处理逻辑
- 调整任务优先级

#### 4. Redis连接失败
**原因**: Redis服务异常
**解决**:
- 检查Redis服务状态
- 检查连接配置
- 重启Redis服务

### 性能调优建议

1. **Worker配置**:
   - 根据CPU核心数配置Worker数量
   - 设置合适的并发数
   - 配置内存限制

2. **Redis优化**:
   - 配置持久化策略
   - 设置合适的内存限制
   - 启用压缩

3. **任务优化**:
   - 合理设置任务优先级
   - 避免提交过大的文本
   - 实现任务去重机制

