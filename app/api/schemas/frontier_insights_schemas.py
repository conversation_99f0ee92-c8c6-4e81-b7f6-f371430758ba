from pydantic import BaseModel, Field
from typing import Optional, Dict, Any

from app.llm_integrations.schemas.frontier_insights_schemas import FrontierInsightsResult


class FrontierInsightsResponse(BaseModel):
    """
    前沿动态模型。
    Attributes:
        type (str): 前沿动态的分类或类型。
        content (str): 该前沿动态信息的内容描述。
        keywords (list): 该前沿动态中的一些关键词。
        hotness (str): 对提取到的前沿动态的一个热度评估。
    """
    type: str = Field(..., description="前沿动态的分类或类型")
    content: str = Field(..., description="该前沿动态信息的内容描述")
    keywords: list[str] = Field(..., description="该前沿动态中的一些关键词")
    hotness: str = Field(..., description="对提取到的前沿动态的一个热度评估，分为三个等级：高、中、低")


class FrontierInsightsResultResponse(BaseModel):
    """
    前沿动态提取结果模型。
    Attributes:
        content (str): 原始内容。
        results (list): 提取的前沿动态列表。
        time (str): 提取时间。
        total (int): 提取前沿动态总数。
    """
    content: str = Field(..., description="原始内容")
    results: list[FrontierInsightsResponse] = Field(..., description="提取的前沿动态列表")
    time: str = Field(..., description="提取时间")
    total: int = Field(..., description="提取前沿动态总数")

    @classmethod
    def from_source_result(cls, result: FrontierInsightsResult) -> 'FrontierInsightsResultResponse':
        """
        工厂类方法：从 FrontierInsightsResult 对象创建 FrontierInsightsResultResponse 实例。

        Args:
            result (FrontierInsightsResult): 源数据对象。

        Returns:
            FrontierInsightsResultResponse: 一个新的、已填充数据的响应对象实例。
        """
        converted_insights = []
        # 遍历源对象中的每个前沿动态
        for insight in result.results:
            # 创建转换后的前沿动态对象
            converted_insight = FrontierInsightsResponse(
                type=insight.type,
                content=insight.content,
                keywords=insight.keywords,
                hotness=insight.hotness
            )
            converted_insights.append(converted_insight)

        # 使用 `cls` (即 FrontierInsightsResultResponse 类本身) 来创建最终实例
        return cls(
            content=result.content,
            results=converted_insights,
            time=result.time.strftime('%Y-%m-%d %H:%M:%S'),
            total=result.total
        )


class FrontierInsightsRequest(BaseModel):
    """
    前沿动态提取请求模型。
    Attributes:
        content (str): 前沿动态内容。
        prompt_variables (Optional[Dict[str, Any]]): 用于格式化prompt的额外变量。
    """
    content: str = Field(..., description="前沿动态内容")
    prompt_variables: Optional[Dict[str, Any]] = Field(None, description="用于格式化prompt的额外变量")
