from pydantic import BaseModel, Field
from typing import Optional, Dict, Any

from app.llm_integrations.schemas.event_extraction_schemas import EventExtractionResult


class EventExtractionArgument(BaseModel):
    """
    事件论元模型。
    Attributes:
        role (str): 论元角色。
        values (list): 论元列表。
    """
    role: str = Field(..., description="论元角色")
    values: list[str] = Field(..., description="论元列表")


class EventExtractionResponse(BaseModel):
    """
    事件模型。
    Attributes:
        type (str): 事件类型。
        trigger (str): 事件触发词。
        description (str): 事件描述。
        arguments (list): 事件论元。
    """
    type: str = Field(..., description="事件类型")
    trigger: str = Field(..., description="事件触发词")
    description: str = Field(..., description="事件描述")
    arguments: list[EventExtractionArgument] = Field(..., description="事件论元")


class EventExtractionResultResponse(BaseModel):
    """
    事件提取结果模型。
    Attributes:
        content (str): 原始内容。
        events (list): 提取的事件列表。
        time (datetime): 提取时间。
        total (int): 提取事件总数。
    """
    content: str = Field(..., description="原始内容")
    results: list[EventExtractionResponse] = Field(..., description="提取的事件列表")
    time: str = Field(..., description="提取时间")
    total: int = Field(..., description="提取事件总数")

    @classmethod
    def from_source_result(cls, result: EventExtractionResult) -> 'EventExtractionResultResponse':
        """
        工厂类方法：从 EventExtractionResult 对象创建 EventExtractionResultResponse 实例。

        Args:
            result (EventExtractionResult): 源数据对象。

        Returns:
            EventExtractionResultResponse: 一个新的、已填充数据的响应对象实例。
        """
        converted_events = []
        # 遍历源对象中的每个事件
        for event in result.results:
            # 1. 转换论元列表
            converted_arguments = [
                EventExtractionArgument(role=arg.role, values=arg.values)
                for arg in event.arguments
            ]

            # 2. 创建转换后的事件对象
            converted_event = EventExtractionResponse(
                type=event.type,
                trigger=event.trigger,
                description=event.description,
                arguments=converted_arguments
            )
            converted_events.append(converted_event)

        # 3. 使用 `cls` (即 EventExtractionResultResponse 类本身) 来创建最终实例
        return cls(
            content=result.content,
            results=converted_events,
            time=result.time.strftime('%Y-%m-%d %H:%M:%S'),
            total=result.total
        )


class EventExtractionRequest(BaseModel):
    """
    事件提取请求模型。
    Attributes:
        content (str): 事件内容。
        prompt_variables (Optional[Dict[str, Any]]): 用于格式化prompt的额外变量。
    """
    content: str = Field(..., description="事件内容")
    prompt_variables: Optional[Dict[str, Any]] = Field(None, description="用于格式化prompt的额外变量")
