from pydantic import BaseModel, Field
from typing import Optional, Dict, Any

from app.llm_integrations.schemas.nearby_hotspot_schemas import NearbyHotspotInfoResult


class NearbyHotspotResponse(BaseModel):
    """
    周边热点事件模型。
    Attributes:
        influence_area (str): 影响区域。
        event_summary (str): 事件摘要。
        threat_level (str): 威胁等级。
        threat_analysis (str): 评定威胁等级的依据。
    """
    influence_area: str = Field(..., description="影响区域")
    event_summary: str = Field(..., description="事件摘要")
    threat_level: str = Field(..., description="威胁等级，分为三个等级：高、中、低")
    threat_analysis: str = Field(..., description="评定威胁等级的依据")


class NearbyHotspotResultResponse(BaseModel):
    """
    周边热点事件提取结果模型。
    Attributes:
        content (str): 原始内容。
        results (list): 提取的周边热点事件列表。
        time (str): 提取时间。
        total (int): 提取周边热点事件总数。
    """
    content: str = Field(..., description="原始内容")
    results: list[NearbyHotspotResponse] = Field(..., description="提取的周边热点事件列表")
    time: str = Field(..., description="提取时间")
    total: int = Field(..., description="提取周边热点事件总数")

    @classmethod
    def from_source_result(cls, result: NearbyHotspotInfoResult) -> 'NearbyHotspotResultResponse':
        """
        工厂类方法：从 NearbyHotspotInfoResult 对象创建 NearbyHotspotResultResponse 实例。

        Args:
            result (NearbyHotspotInfoResult): 源数据对象。

        Returns:
            NearbyHotspotResultResponse: 一个新的、已填充数据的响应对象实例。
        """
        converted_hotspots = []
        # 遍历源对象中的每个周边热点事件
        for hotspot in result.results:
            # 创建转换后的周边热点事件对象
            converted_hotspot = NearbyHotspotResponse(
                influence_area=hotspot.influence_area,
                event_summary=hotspot.event_summary,
                threat_level=hotspot.threat_level,
                threat_analysis=hotspot.threat_analysis
            )
            converted_hotspots.append(converted_hotspot)

        # 使用 `cls` (即 NearbyHotspotResultResponse 类本身) 来创建最终实例
        return cls(
            content=result.content,
            results=converted_hotspots,
            time=result.time.strftime('%Y-%m-%d %H:%M:%S'),
            total=result.total
        )


class NearbyHotspotRequest(BaseModel):
    """
    周边热点事件提取请求模型。
    Attributes:
        content (str): 周边热点事件内容。
        prompt_variables (Optional[Dict[str, Any]]): 用于格式化prompt的额外变量。
    """
    content: str = Field(..., description="周边热点事件内容")
    prompt_variables: Optional[Dict[str, Any]] = Field(None, description="用于格式化prompt的额外变量")
