from pydantic import BaseModel, Field
from typing import Optional, Dict, Any

from app.llm_integrations.schemas.general_schemas import GeneralInfoResult


class GeneralResponse(BaseModel):
    """
    通用LLM调用响应模型。
    
    Attributes:
        content (str): 处理后的内容。
        processing_info (Optional[Dict[str, Any]]): 处理过程信息（可选）。
    """
    content: str = Field(..., description="处理后的内容")
    processing_info: Optional[Dict[str, Any]] = Field(None, description="处理过程信息，如使用的模式、处理时间等")


class GeneralResultResponse(BaseModel):
    """
    通用LLM调用结果响应模型。
    
    Attributes:
        content (str): 处理后的内容。
        time (str): 处理完成时间。
        processing_info (Optional[Dict[str, Any]]): 处理过程信息。
    """
    content: str = Field(..., description="处理后的内容")
    time: str = Field(..., description="处理完成时间")
    processing_info: Optional[Dict[str, Any]] = Field(None, description="处理过程信息")

    @classmethod
    def from_source_result(cls, result: GeneralInfoResult, processing_info: Optional[Dict[str, Any]] = None) -> 'GeneralResultResponse':
        """
        从 GeneralInfoResult 对象创建 GeneralResultResponse 实例。

        Args:
            result (GeneralInfoResult): 来自服务层的通用处理结果对象。
            processing_info (Optional[Dict[str, Any]]): 额外的处理信息。

        Returns:
            GeneralResultResponse: 转换后的API响应对象。
        """
        # 构建处理信息
        final_processing_info = {}
        if processing_info:
            final_processing_info.update(processing_info)
        
        # 添加基础处理信息
        final_processing_info.update({
            'content_length': len(result.content),
            'processed_at': result.time.isoformat(),
        })

        # 使用 `cls` (即 GeneralResultResponse 类本身) 来创建最终实例
        return cls(
            content=result.content,
            time=result.time.strftime('%Y-%m-%d %H:%M:%S'),
            processing_info=final_processing_info
        )


class GeneralRequest(BaseModel):
    """
    通用LLM调用请求模型。
    
    Attributes:
        content (str): 需要处理的文本内容。
        prompt_variables (Optional[Dict[str, Any]]): 用于格式化prompt的额外变量。
        processing_options (Optional[Dict[str, Any]]): 处理选项配置。
    """
    content: str = Field(..., description="需要处理的文本内容", min_length=1)
    prompt_variables: Optional[Dict[str, Any]] = Field(
        None, 
        description="用于格式化prompt的额外变量，通常包含map_prompt和combine_prompt"
    )
    processing_options: Optional[Dict[str, Any]] = Field(
        None,
        description="处理选项配置，如是否启用Map-Reduce模式等"
    )

    class Config:
        """Pydantic配置"""
        json_schema_extra = {
            "example": {
                "content": "这是需要处理的文本内容。可以是任意长度的文本。",
                "prompt_variables": {
                    "map_prompt": "请分析以下文本内容：\n{text}\n\n请提供详细的分析结果。",
                    "combine_prompt": "请整合以下分析结果：\n{text}\n\n请提供最终的综合分析。"
                },
                "processing_options": {
                    "enable_map_reduce": True,
                    "chunk_size": 8000
                }
            }
        }


class GeneralBatchRequest(BaseModel):
    """
    通用LLM调用批量请求模型。
    
    Attributes:
        items (list[GeneralRequest]): 批量处理的请求列表。
        batch_options (Optional[Dict[str, Any]]): 批量处理选项。
    """
    items: list[GeneralRequest] = Field(..., description="批量处理的请求列表", min_items=1, max_items=10)
    batch_options: Optional[Dict[str, Any]] = Field(
        None,
        description="批量处理选项，如并发数量、超时设置等"
    )

    class Config:
        """Pydantic配置"""
        json_schema_extra = {
            "example": {
                "items": [
                    {
                        "content": "第一个需要处理的文本内容。",
                        "prompt_variables": {
                            "map_prompt": "请分析文本：{text}",
                            "combine_prompt": "请整合结果：{text}"
                        }
                    },
                    {
                        "content": "第二个需要处理的文本内容。",
                        "prompt_variables": {
                            "map_prompt": "请分析文本：{text}",
                            "combine_prompt": "请整合结果：{text}"
                        }
                    }
                ],
                "batch_options": {
                    "max_concurrent": 3,
                    "timeout_seconds": 300
                }
            }
        }


class GeneralBatchResponse(BaseModel):
    """
    通用LLM调用批量响应模型。
    
    Attributes:
        results (list[GeneralResultResponse]): 批量处理的结果列表。
        batch_info (Dict[str, Any]): 批量处理信息。
    """
    results: list[GeneralResultResponse] = Field(..., description="批量处理的结果列表")
    batch_info: Dict[str, Any] = Field(..., description="批量处理信息，如总数、成功数、失败数等")

    @classmethod
    def from_batch_results(
        cls, 
        results: list[GeneralInfoResult], 
        batch_info: Optional[Dict[str, Any]] = None
    ) -> 'GeneralBatchResponse':
        """
        从批量 GeneralInfoResult 对象创建 GeneralBatchResponse 实例。

        Args:
            results (list[GeneralInfoResult]): 批量处理结果列表。
            batch_info (Optional[Dict[str, Any]]): 批量处理信息。

        Returns:
            GeneralBatchResponse: 转换后的API响应对象。
        """
        # 转换每个结果
        converted_results = []
        for i, result in enumerate(results):
            processing_info = {"batch_index": i}
            converted_result = GeneralResultResponse.from_source_result(result, processing_info)
            converted_results.append(converted_result)

        # 构建批量信息
        final_batch_info = {
            "total_items": len(results),
            "successful_items": len([r for r in results if r.content]),
            "failed_items": len([r for r in results if not r.content]),
        }
        
        if batch_info:
            final_batch_info.update(batch_info)

        return cls(
            results=converted_results,
            batch_info=final_batch_info
        )


class GeneralStatsResponse(BaseModel):
    """
    通用LLM调用统计信息响应模型。
    
    Attributes:
        stats (Dict[str, Any]): 统计信息字典。
    """
    stats: Dict[str, Any] = Field(..., description="处理统计信息")

    class Config:
        """Pydantic配置"""
        json_schema_extra = {
            "example": {
                "stats": {
                    "total_requests": 10,
                    "successful_requests": 9,
                    "failed_requests": 1,
                    "total_content_processed": 9,
                    "average_content_length": 1500.5,
                    "map_reduce_usage_rate": 30.0,
                    "content_size_distribution": {
                        "small": 5,
                        "medium": 3,
                        "large": 1
                    },
                    "processing_mode_stats": {
                        "single_mode": 7,
                        "map_reduce_mode": 2
                    },
                    "average_processing_time_ms": 2500.0,
                    "total_llm_calls": 15,
                    "total_llm_tokens_generated": 5000
                }
            }
        }
