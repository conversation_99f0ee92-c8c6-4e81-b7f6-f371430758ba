from pydantic import BaseModel, ConfigDict, Field
from typing import Any, Generic, Optional, TypeVar
from pydantic.alias_generators import to_camel
from pydantic.functional_validators import model_validator

T = TypeVar('T')


class ApiResponse(BaseModel, Generic[T]):
    """
    通用 API 响应模型。

    Attributes:
        status (str): 响应状态，例如 "success" 或 "error"。
        msg (Optional[str]): 响应消息，可选。
        data (Optional[T]): 响应数据，可以是任意类型，可选。
        error (Optional[str]): 错误信息，可选。
    """
    status: str = Field(..., description="响应状态")
    msg: Optional[str] = Field(default=None, description="响应消息")
    data: Optional[T] = Field(default=None, description="响应数据")
    error: Optional[str] = Field(default=None, description="错误信息")

    model_config = ConfigDict(
        title="接口通用返回体",
        #  自动生成别名
        alias_generator=to_camel,
        #  使用字段别名
        populate_by_name=True,
        # 允许额外的字段
        extra="allow",
        # 对已创建实例的字段赋值也进行验证
        validate_assignment=True,
        json_schema_extra={
            "examples": [
                {
                    "status": "success",
                    "msg": "server is healthy",
                    "data": {
                        "name": "LlmFlowHub",
                        "version": "1.0.0"
                    }
                },
                {
                    "status": "error",
                    "msg": "Not Found",
                    "error": "Resource not found"
                }
            ]
        },
    )

    @model_validator(mode='after')
    def check_data_or_error(self):
        """
        验证 'data' 或 'error' 属性必须至少有一个不为 None。
        """
        if self.data is None and self.error is None:
            raise ValueError(" 'data' 或 'error' 属性必须至少有一个不为None")
        return self
