from fastapi import APIRouter
from app.api.endpoints import health
from app.api.endpoints.event_extraction import event_extraction
from app.api.endpoints.frontier_insights import frontier_insights
from app.api.endpoints.nearby_hotspot import nearby_hotspot
from app.api.endpoints.tasks import task_management
from app.api.endpoints.general import general

api_router = APIRouter()

api_router.include_router(health.router, prefix='/api/v1', tags=["健康检查"])
api_router.include_router(event_extraction.router, prefix='/api/v1/event-extraction', tags=["事件抽取"])
api_router.include_router(frontier_insights.router, prefix='/api/v1/frontier-insights', tags=["前沿动态提取"])
api_router.include_router(nearby_hotspot.router, prefix='/api/v1/nearby-hotspot', tags=["周边热点事件提取"])
api_router.include_router(general.router, prefix='/api/v1/general', tags=["通用LLM调用"])
api_router.include_router(task_management.router, prefix='/api/v1/tasks', tags=["任务管理"])
