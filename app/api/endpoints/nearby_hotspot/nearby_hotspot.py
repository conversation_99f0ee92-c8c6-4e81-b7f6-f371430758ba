from fastapi import APIRouter, HTTPException

from app.api.schemas.nearby_hotspot_schemas import NearbyHotspotRequest, NearbyHotspotResultResponse
from app.api.schemas.response_schemas import ApiResponse
from app.core.logger_config import get_logger
from app.services.nearby_hotspot import extract_hotspots as extract_hotspots_service

# 获取日志记录器
logger = get_logger()

router = APIRouter()


@router.get("/")
async def root():
    return {"message": "周边热点事件提取服务"}


@router.post("/", response_model=ApiResponse[NearbyHotspotResultResponse],
             response_model_exclude_none=True)
async def extract_hotspots(nearby_hotspot_request: NearbyHotspotRequest):
    """
    从文本中提取周边热点事件信息。

    Args:
        nearby_hotspot_request (NearbyHotspotRequest): 包含需要提取周边热点事件的文本的请求对象。

    Returns:
        ApiResponse[NearbyHotspotResultResponse]: 包含提取到的周边热点事件信息的响应对象。
    """
    try:
        hotspots_result = extract_hotspots_service(
            nearby_hotspot_request.content,
            prompt_variables=nearby_hotspot_request.prompt_variables
        )
        nearby_hotspot_response: NearbyHotspotResultResponse = NearbyHotspotResultResponse.from_source_result(hotspots_result)
        return ApiResponse(status='success', msg='周边热点事件提取成功', data=nearby_hotspot_response)

    except Exception as e:
        logger.error(f"周边热点事件提取失败: {str(e)}")
        error_response = ApiResponse(status='error', msg='周边热点事件提取失败', error=str(e))
        return HTTPException(status_code=400, detail=error_response)
