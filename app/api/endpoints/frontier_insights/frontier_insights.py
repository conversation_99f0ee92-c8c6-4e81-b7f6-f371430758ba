from fastapi import APIRouter, HTTPException

from app.api.schemas.frontier_insights_schemas import FrontierInsightsRequest, FrontierInsightsResultResponse
from app.api.schemas.response_schemas import ApiResponse
from app.core.logger_config import get_logger
from app.services.frontier_insights import extract_insights as extract_insights_service

# 获取日志记录器
logger = get_logger()

router = APIRouter()


@router.get("/")
async def root():
    return {"message": "前沿动态提取服务"}


@router.post("/", response_model=ApiResponse[FrontierInsightsResultResponse],
             response_model_exclude_none=True)
async def extract_insights(frontier_insights_request: FrontierInsightsRequest):
    """
    从文本中提取前沿动态信息。

    Args:
        frontier_insights_request (FrontierInsightsRequest): 包含需要提取前沿动态的文本的请求对象。

    Returns:
        ApiResponse[FrontierInsightsResultResponse]: 包含提取到的前沿动态信息的响应对象。
    """
    try:
        insights_result = extract_insights_service(
            frontier_insights_request.content,
            prompt_variables=frontier_insights_request.prompt_variables
        )
        frontier_insights_response: FrontierInsightsResultResponse = FrontierInsightsResultResponse.from_source_result(insights_result)
        return ApiResponse(status='success', msg='前沿动态提取成功', data=frontier_insights_response)

    except Exception as e:
        logger.error(f"前沿动态提取失败: {str(e)}")
        error_response = ApiResponse(status='error', msg='前沿动态提取失败', error=str(e))
        return HTTPException(status_code=400, detail=error_response)
