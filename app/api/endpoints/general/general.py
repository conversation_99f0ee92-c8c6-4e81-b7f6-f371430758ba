from fastapi import APIRouter, HTTPException
from typing import List

from app.api.schemas.general_schemas import (
    GeneralRequest, 
    GeneralResultResponse, 
    GeneralBatchRequest, 
    GeneralBatchResponse,
    GeneralStatsResponse
)
from app.api.schemas.response_schemas import ApiResponse
from app.core.logger_config import get_logger
from app.services.general import process_text as process_text_service, general_service

# 获取日志记录器
logger = get_logger()

router = APIRouter()


@router.get("/")
async def root():
    """通用LLM调用服务根端点"""
    return {"message": "通用LLM调用服务"}


@router.post("/", response_model=ApiResponse[GeneralResultResponse],
             response_model_exclude_none=True)
async def process_text(general_request: GeneralRequest):
    """
    通用LLM文本处理接口。

    支持动态prompt变量注入，可以处理任意类型的文本内容。
    根据文本长度自动选择单次处理或Map-Reduce模式。

    Args:
        general_request (GeneralRequest): 包含需要处理的文本和prompt变量的请求对象。

    Returns:
        ApiResponse[GeneralResultResponse]: 包含处理结果的响应对象。
    """
    try:
        # 验证请求
        if not general_request.content.strip():
            raise ValueError("请求内容不能为空")

        # 记录请求信息
        logger.info(f"收到通用LLM处理请求，内容长度: {len(general_request.content)}")
        if general_request.prompt_variables:
            logger.debug(f"包含prompt变量: {list(general_request.prompt_variables.keys())}")

        # 调用服务层处理
        result = process_text_service(
            general_request.content,
            prompt_variables=general_request.prompt_variables
        )

        # 获取处理统计信息
        processing_info = {}
        if general_request.processing_options:
            processing_info.update(general_request.processing_options)
        
        # 添加统计信息
        stats = general_service.get_processing_stats()
        if stats:
            processing_info['stats_summary'] = {
                'total_requests': stats.get('total_requests', 0),
                'average_processing_time_ms': stats.get('average_processing_time_ms', 0),
                'map_reduce_usage_rate': stats.get('map_reduce_usage_rate', 0)
            }

        # 构建响应
        general_response = GeneralResultResponse.from_source_result(result, processing_info)
        
        logger.info(f"通用LLM处理成功，结果长度: {len(result.content)}")
        return ApiResponse(status='success', msg='文本处理成功', data=general_response)

    except ValueError as e:
        logger.warning(f"请求参数错误: {str(e)}")
        error_response = ApiResponse(status='error', msg='请求参数错误', error=str(e))
        raise HTTPException(status_code=400, detail=error_response.model_dump())
    except Exception as e:
        logger.error(f"通用LLM处理失败: {str(e)}")
        error_response = ApiResponse(status='error', msg='文本处理失败', error=str(e))
        raise HTTPException(status_code=500, detail=error_response.model_dump())


@router.post("/batch", response_model=ApiResponse[GeneralBatchResponse],
             response_model_exclude_none=True)
async def process_batch(batch_request: GeneralBatchRequest):
    """
    批量通用LLM文本处理接口。

    支持同时处理多个文本内容，每个可以有独立的prompt变量。

    Args:
        batch_request (GeneralBatchRequest): 包含多个处理请求的批量请求对象。

    Returns:
        ApiResponse[GeneralBatchResponse]: 包含批量处理结果的响应对象。
    """
    try:
        # 验证批量请求
        if not batch_request.items:
            raise ValueError("批量请求不能为空")

        logger.info(f"收到批量通用LLM处理请求，包含 {len(batch_request.items)} 个项目")

        # 处理每个请求
        results = []
        successful_count = 0
        failed_count = 0

        for i, item in enumerate(batch_request.items):
            try:
                logger.debug(f"处理批量请求第 {i+1}/{len(batch_request.items)} 项")
                
                # 验证单个请求
                if not item.content.strip():
                    logger.warning(f"批量请求第 {i+1} 项内容为空，跳过")
                    failed_count += 1
                    continue

                # 调用服务层处理
                result = process_text_service(
                    item.content,
                    prompt_variables=item.prompt_variables
                )
                results.append(result)
                successful_count += 1

            except Exception as e:
                logger.error(f"批量请求第 {i+1} 项处理失败: {str(e)}")
                failed_count += 1
                # 继续处理其他项目

        # 构建批量处理信息
        batch_info = {
            'total_items': len(batch_request.items),
            'successful_items': successful_count,
            'failed_items': failed_count,
            'success_rate': (successful_count / len(batch_request.items)) * 100 if batch_request.items else 0
        }

        # 添加批量选项信息
        if batch_request.batch_options:
            batch_info['batch_options'] = batch_request.batch_options

        # 构建响应
        batch_response = GeneralBatchResponse.from_batch_results(results, batch_info)
        
        logger.info(f"批量通用LLM处理完成，成功: {successful_count}, 失败: {failed_count}")
        return ApiResponse(status='success', msg='批量文本处理完成', data=batch_response)

    except ValueError as e:
        logger.warning(f"批量请求参数错误: {str(e)}")
        error_response = ApiResponse(status='error', msg='批量请求参数错误', error=str(e))
        raise HTTPException(status_code=400, detail=error_response.model_dump())
    except Exception as e:
        logger.error(f"批量通用LLM处理失败: {str(e)}")
        error_response = ApiResponse(status='error', msg='批量文本处理失败', error=str(e))
        raise HTTPException(status_code=500, detail=error_response.model_dump())


@router.get("/stats", response_model=ApiResponse[GeneralStatsResponse],
            response_model_exclude_none=True)
async def get_processing_stats():
    """
    获取通用LLM处理统计信息。

    Returns:
        ApiResponse[GeneralStatsResponse]: 包含处理统计信息的响应对象。
    """
    try:
        logger.debug("获取通用LLM处理统计信息")
        
        # 获取统计信息
        stats = general_service.get_processing_stats()
        
        if stats is None:
            # 如果没有统计信息，返回空统计
            stats = {
                'total_requests': 0,
                'successful_requests': 0,
                'failed_requests': 0,
                'message': '暂无处理统计信息'
            }

        # 构建响应
        stats_response = GeneralStatsResponse(stats=stats)
        
        logger.debug("成功获取通用LLM处理统计信息")
        return ApiResponse(status='success', msg='获取统计信息成功', data=stats_response)

    except Exception as e:
        logger.error(f"获取统计信息失败: {str(e)}")
        error_response = ApiResponse(status='error', msg='获取统计信息失败', error=str(e))
        raise HTTPException(status_code=500, detail=error_response.model_dump())


@router.post("/stats/reset", response_model=ApiResponse[dict])
async def reset_processing_stats():
    """
    重置通用LLM处理统计信息。

    Returns:
        ApiResponse[dict]: 重置操作结果。
    """
    try:
        logger.info("重置通用LLM处理统计信息")
        
        # 重置统计信息
        general_service.reset_stats()
        
        result = {
            'message': '统计信息已重置',
            'reset_at': general_service.general_service._chain.callback_handler.stats.get('reset_time', 'unknown') if general_service._chain else 'unknown'
        }
        
        logger.info("成功重置通用LLM处理统计信息")
        return ApiResponse(status='success', msg='统计信息重置成功', data=result)

    except Exception as e:
        logger.error(f"重置统计信息失败: {str(e)}")
        error_response = ApiResponse(status='error', msg='重置统计信息失败', error=str(e))
        raise HTTPException(status_code=500, detail=error_response.model_dump())


@router.post("/validate-prompt", response_model=ApiResponse[dict])
async def validate_prompt_variables(prompt_variables: dict):
    """
    验证prompt变量格式。

    Args:
        prompt_variables (dict): 需要验证的prompt变量。

    Returns:
        ApiResponse[dict]: 验证结果。
    """
    try:
        logger.debug("验证prompt变量格式")
        
        # 验证prompt变量
        formatted_variables = general_service.validate_prompt_format(prompt_variables)
        
        result = {
            'valid': True,
            'formatted_variables': formatted_variables,
            'variable_count': len(formatted_variables),
            'message': 'prompt变量格式有效'
        }
        
        logger.debug("prompt变量格式验证成功")
        return ApiResponse(status='success', msg='prompt变量验证成功', data=result)

    except ValueError as e:
        logger.warning(f"prompt变量格式无效: {str(e)}")
        result = {
            'valid': False,
            'error': str(e),
            'message': 'prompt变量格式无效'
        }
        return ApiResponse(status='error', msg='prompt变量验证失败', data=result)
    except Exception as e:
        logger.error(f"验证prompt变量失败: {str(e)}")
        error_response = ApiResponse(status='error', msg='验证prompt变量失败', error=str(e))
        raise HTTPException(status_code=500, detail=error_response.model_dump())
