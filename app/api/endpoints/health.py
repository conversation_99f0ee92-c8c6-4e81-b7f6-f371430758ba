from fastapi import APIRouter, Request
from app.core.config import settings
from app.api.schemas.health_schemas import HealthCheck
from app.api.schemas.response_schemas import ApiResponse
from app.core.logger_config import get_logger

# 获取日志记录器
logger = get_logger()

router = APIRouter()


@router.get("/health/", response_model=ApiResponse[HealthCheck], response_model_exclude_none=True)
async def health_check_endpoint(request: Request):
    """
    提供一个简单的健康检查端点。

    Args:
        request: FastAPI 请求对象

    Returns:
        HealthCheck: 包含应用名称、版本和描述的 Pydantic 模型。
    """
    # 记录健康检查请求的日志
    client_host = request.client.host if request.client else "未知客户端"
    logger.info(f"收到来自 {client_host} 的健康检查请求")

    # 使用不同级别的日志记录示例
    logger.debug(f"健康检查详情: 请求路径={request.url.path}, 方法={request.method}")

    # 返回健康检查响应
    health_check = HealthCheck(
        name=settings.PROJECT_NAME,
        version=settings.PROJECT_VERSION,
        description=settings.PROJECT_DESCRIPTION
    )

    logger.info(f"健康检查响应: {health_check.model_dump()}")
    response = ApiResponse(status='success', msg='健康检查成功', data=health_check)
    return response
