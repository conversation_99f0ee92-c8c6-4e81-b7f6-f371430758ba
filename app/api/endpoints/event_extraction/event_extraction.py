from fastapi import APIRouter, HTTPException

from app.api.schemas.event_extraction_schemas import EventExtractionRequest, EventExtractionResultResponse
from app.api.schemas.response_schemas import ApiResponse
from app.core.logger_config import get_logger
from app.services.event_extraction import extract_events as extract_events_service

# 获取日志记录器
logger = get_logger()

router = APIRouter()


@router.get("/")
async def root():
    return {"message": "事件提取服务"}


@router.post("/", response_model=ApiResponse[EventExtractionResultResponse],
             response_model_exclude_none=True)
async def extract_events(event_extraction_request: EventExtractionRequest):
    """
    从文本中提取事件信息。

    Args:
        event_extraction_request (EventExtractionRequest): 包含需要提取事件的文本的请求对象。

    Returns:
        ApiResponse[EventExtractionResultResponse]: 包含提取到的事件信息的响应对象。
    """
    try:
        events_result = extract_events_service(
            event_extraction_request.content,
            prompt_variables=event_extraction_request.prompt_variables
        )
        event_extraction_response: EventExtractionResultResponse = EventExtractionResultResponse.from_source_result(events_result)
        return ApiResponse(status='success', msg='事件提取成功', data=event_extraction_response)

    except Exception as e:
        logger.error(f"事件提取失败: {str(e)}")
        error_response = ApiResponse(status='error', msg='事件提取失败', error=str(e))
        return HTTPException(status_code=400, detail=error_response)
