from datetime import datetime
from typing import Optional
from fastapi import APIRouter, HTTPException, Query
from celery.result import AsyncResult
from celery import states

from app.api.schemas.task_schemas import (
    TaskSubmitRequest, TaskSubmitResponse, TaskStatusResponse, 
    TaskResultResponse, TaskListRequest, TaskListResponse,
    TaskCancelRequest, TaskCancelResponse, TaskStatus, TaskType
)
from app.api.schemas.response_schemas import ApiResponse
from app.tasks.celery_app import celery_app
from app.tasks.llm_tasks import event_extraction_task, frontier_insights_task, nearby_hotspot_task
from app.core.logger_config import get_logger
from app.utils.redis_utils import test_redis_connections, get_redis_queue_info

logger = get_logger()
router = APIRouter()


@router.post("/submit", response_model=ApiResponse[TaskSubmitResponse])
async def submit_task(request: TaskSubmitRequest):
    """
    提交异步任务。
    
    Args:
        request: 任务提交请求
        
    Returns:
        ApiResponse[TaskSubmitResponse]: 任务提交响应
    """
    try:
        logger.info(f"收到任务提交请求，类型: {request.task_type}, 内容长度: {len(request.content)}")
        
        # 准备任务参数
        task_args = [request.content, request.callback_url, request.echo]
        task_kwargs = {
            "prompt_variables": request.prompt_variables
        }
        
        # 根据任务类型分发到不同的Celery任务
        if request.task_type == TaskType.EVENT_EXTRACTION:
            # 提交事件提取任务
            task = event_extraction_task.apply_async(
                args=task_args,
                kwargs=task_kwargs,
                priority=request.priority
            )
        elif request.task_type == TaskType.FRONTIER_INSIGHTS:
            # 提交前沿动态提取任务
            task = frontier_insights_task.apply_async(
                args=task_args,
                kwargs=task_kwargs,
                priority=request.priority
            )
        elif request.task_type == TaskType.NEARBY_HOTSPOT:
            # 提交周边热点事件提取任务
            task = nearby_hotspot_task.apply_async(
                args=task_args,
                kwargs=task_kwargs,
                priority=request.priority
            )
        else:
            raise HTTPException(status_code=400, detail=f"不支持的任务类型: {request.task_type}")
        
        # 构建响应
        response = TaskSubmitResponse(
            task_id=task.id,
            task_type=request.task_type,
            status=TaskStatus.PENDING,
            submitted_at=datetime.now(),
            estimated_completion_time=None,  # 可以根据历史数据估算
            echo=request.echo
        )
        
        logger.info(f"任务提交成功，任务ID: {task.id}")
        return ApiResponse(status='success', msg='任务提交成功', data=response)
        
    except Exception as e:
        logger.error(f"任务提交失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"任务提交失败: {str(e)}")


@router.get("/result/{task_id}", response_model=ApiResponse[TaskResultResponse])
async def get_task_result(task_id: str):
    """
    获取任务结果和状态。

    Args:
        task_id: 任务ID

    Returns:
        ApiResponse[TaskResultResponse]: 任务结果响应（包含状态信息）
    """
    try:
        logger.debug(f"获取任务结果和状态，任务ID: {task_id}")

        # 获取任务结果对象
        task_result = AsyncResult(task_id, app=celery_app)

        # 检查任务是否存在（对于新提交的任务，可能暂时没有info）
        if task_result.state == states.PENDING and not task_result.info:
            # 对于刚提交的任务，返回基本信息
            response = TaskResultResponse(
                task_id=task_id,
                task_type=TaskType.EVENT_EXTRACTION,  # 默认类型，后续会被更新
                status=TaskStatus.PENDING,
                result=None,
                submitted_at=datetime.now(),
                started_at=None,
                completed_at=None,
                processing_time=None,
                error_message=None,
                progress=None,
                retry_count=0,
                echo=None
            )
            return ApiResponse(status='success', msg='获取任务信息成功', data=response)

        # 获取任务信息（对于未完成的任务）或结果数据（对于已完成的任务）
        if task_result.state in [states.SUCCESS, states.FAILURE]:
            # 任务已完成，从结果中获取数据
            data = task_result.result or {}
        else:
            # 任务未完成，从info中获取状态数据
            data = task_result.info or {}

        # 获取任务类型
        task_type_str = data.get('task_type')
        if not task_type_str:
            # 如果没有任务类型信息，可能是旧任务或损坏的任务
            raise HTTPException(status_code=404, detail="任务信息不完整，无法确定任务类型")

        # 根据任务状态决定是否包含结果数据
        result_data = None
        if task_result.state == states.SUCCESS:
            result_data = data.get('result')

        # 构建统一的响应
        response = TaskResultResponse(
            task_id=task_id,
            task_type=TaskType(task_type_str),
            status=TaskStatus(task_result.state),
            result=result_data,
            submitted_at=datetime.fromisoformat(data.get('submitted_at', datetime.now().isoformat())),
            started_at=datetime.fromisoformat(data['started_at']) if data.get('started_at') else None,
            completed_at=datetime.fromisoformat(data['completed_at']) if data.get('completed_at') else None,
            processing_time=data.get('processing_time'),
            error_message=data.get('error_message'),
            progress=data.get('progress'),
            retry_count=task_result.retries or 0,
            echo=data.get('echo')
        )

        return ApiResponse(status='success', msg='获取任务信息成功', data=response)

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"获取任务结果失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"获取任务结果失败: {str(e)}")


@router.post("/cancel", response_model=ApiResponse[TaskCancelResponse])
async def cancel_task(request: TaskCancelRequest):
    """
    取消任务。
    
    Args:
        request: 任务取消请求
        
    Returns:
        ApiResponse[TaskCancelResponse]: 任务取消响应
    """
    try:
        logger.info(f"取消任务，任务ID: {request.task_id}, 原因: {request.reason}")
        
        # 撤销任务
        celery_app.control.revoke(request.task_id, terminate=True)
        
        # 构建响应
        response = TaskCancelResponse(
            task_id=request.task_id,
            status=TaskStatus.REVOKED,
            cancelled_at=datetime.now(),
            reason=request.reason
        )
        
        logger.info(f"任务取消成功，任务ID: {request.task_id}")
        return ApiResponse(status='success', msg='任务取消成功', data=response)
        
    except Exception as e:
        logger.error(f"取消任务失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"取消任务失败: {str(e)}")


@router.get("/list", response_model=ApiResponse[TaskListResponse])
async def list_tasks(
    task_type: Optional[TaskType] = Query(None, description="任务类型过滤"),
    status: Optional[TaskStatus] = Query(None, description="任务状态过滤"),
    limit: int = Query(20, description="返回数量限制", ge=1, le=100),
    offset: int = Query(0, description="偏移量", ge=0)
):
    """
    获取任务列表。

    Args:
        task_type: 任务类型过滤
        status: 任务状态过滤
        limit: 返回数量限制
        offset: 偏移量

    Returns:
        ApiResponse[TaskListResponse]: 任务列表响应
    """
    try:
        logger.debug(f"获取任务列表，过滤条件 - 类型: {task_type}, 状态: {status}, 限制: {limit}, 偏移: {offset}")

        from app.utils.redis_utils import redis_connection

        # 获取Redis客户端
        result_client = redis_connection.result_client

        # 获取所有任务ID（从Redis结果后端）
        # Celery结果键的模式是 celery-task-meta-{task_id}
        task_keys = result_client.keys('celery-task-meta-*')

        all_tasks = []

        # 遍历所有任务键，获取任务信息
        for key in task_keys:
            try:
                # 提取任务ID
                task_id = key.replace('celery-task-meta-', '')

                # 获取任务结果对象
                task_result = AsyncResult(task_id, app=celery_app)

                # 获取任务数据
                if task_result.state in [states.SUCCESS, states.FAILURE]:
                    data = task_result.result or {}
                else:
                    data = task_result.info or {}

                # 检查是否有必要的数据
                if not data.get('task_type'):
                    continue

                # 应用过滤条件
                if task_type and data.get('task_type') != task_type.value:
                    continue

                if status and task_result.state != status.value:
                    continue

                # 构建任务状态响应
                task_status = TaskStatusResponse(
                    task_id=task_id,
                    task_type=TaskType(data.get('task_type')),
                    status=TaskStatus(task_result.state),
                    progress=data.get('progress'),
                    submitted_at=datetime.fromisoformat(data.get('submitted_at', datetime.now().isoformat())),
                    started_at=datetime.fromisoformat(data['started_at']) if data.get('started_at') else None,
                    completed_at=datetime.fromisoformat(data['completed_at']) if data.get('completed_at') else None,
                    error_message=data.get('error_message'),
                    retry_count=task_result.retries or 0,
                    echo=data.get('echo')
                )

                all_tasks.append(task_status)

            except Exception as task_error:
                logger.warning(f"处理任务 {key} 时出错: {str(task_error)}")
                continue

        # 按提交时间倒序排序
        all_tasks.sort(key=lambda x: x.submitted_at, reverse=True)

        # 应用分页
        total = len(all_tasks)
        start_idx = offset
        end_idx = offset + limit
        paginated_tasks = all_tasks[start_idx:end_idx]

        response = TaskListResponse(
            tasks=paginated_tasks,
            total=total,
            limit=limit,
            offset=offset
        )

        return ApiResponse(status='success', msg='获取任务列表成功', data=response)

    except Exception as e:
        logger.error(f"获取任务列表失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"获取任务列表失败: {str(e)}")


@router.get("/health/redis")
async def check_redis_health():
    """
    检查Redis连接健康状态。

    Returns:
        ApiResponse: Redis连接状态
    """
    try:
        logger.debug("检查Redis连接健康状态")

        result = test_redis_connections()

        if result['status'] == 'success':
            return ApiResponse(status='success', msg='Redis连接正常', data=result)
        else:
            return ApiResponse(status='error', msg='Redis连接异常', data=result)

    except Exception as e:
        logger.error(f"Redis健康检查失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Redis健康检查失败: {str(e)}")


@router.get("/health/queue")
async def check_queue_health():
    """
    检查队列健康状态和信息。

    Returns:
        ApiResponse: 队列状态信息
    """
    try:
        logger.debug("检查队列健康状态")

        # 检查Redis连接
        redis_result = test_redis_connections()

        # 获取队列信息（如果Redis可用）
        if redis_result['status'] == 'success':
            queue_info = get_redis_queue_info()
        else:
            queue_info = {
                'status': 'error',
                'message': 'Redis不可用，无法获取队列信息'
            }

        # 获取Celery worker状态
        try:
            inspect = celery_app.control.inspect()
            stats = inspect.stats() or {}
            active = inspect.active() or {}
        except Exception as worker_error:
            logger.warning(f"获取Worker状态失败: {str(worker_error)}")
            stats = {}
            active = {}

        result = {
            'redis_status': redis_result,
            'queue_info': queue_info,
            'worker_stats': stats,
            'active_tasks': active
        }

        overall_status = 'success' if redis_result['status'] == 'success' else 'error'

        return ApiResponse(
            status=overall_status,
            msg='队列健康检查完成',
            data=result
        )

    except Exception as e:
        logger.error(f"队列健康检查失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"队列健康检查失败: {str(e)}")
