# Database session management
from sqlalchemy import create_engine
from sqlalchemy.orm import sessionmaker
from app.core.config import settings
from app.db.models import Base

SQLALCHEMY_DATABASE_URL = settings.DATABASE_URL

engine = create_engine(
    SQLALCHEMY_DATABASE_URL,
    # connect_args={"check_same_thread": False} # 仅 SQLite 需要
    pool_pre_ping=True  # 推荐用于生产环境，检查连接是否有效
)

SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)


def get_db():
    """
    获取数据库会话的依赖项。

    Yields:
        Session: SQLAlchemy 数据库会话。
    """
    db = SessionLocal()
    try:
        yield db
    finally:
        db.close()


def create_tables():
    """
    根据定义的模型在数据库中创建所有表。
    这通常在应用启动时或通过迁移工具执行。
    """
    Base.metadata.create_all(bind=engine)

# 如果你想在应用启动时自动创建表，可以取消下面的注释
# create_tables()
