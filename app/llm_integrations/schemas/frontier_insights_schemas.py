from datetime import datetime
from typing import Dict, List, Any, Optional
from pydantic import BaseModel, Field, field_validator, model_validator


class FrontierInsightsInfo(BaseModel):
    """
    单个提取前沿动态的数据模型。
    封装了一个从文本中识别出的具体事件的所有相关信息。
    """
    type: str = Field(description="前沿动态的分类或类型 (例如: '战略动向', '法规政策')")
    content: str = Field(description="该前沿动态信息的内容描述")
    keywords: List[str] = Field(default_factory=list, description="该前沿动态中的一些关键词")
    hotness: str = Field(description="对提取到的前沿动态的一个热度评估，分为三个等级：高、中、低")

    @field_validator('type')
    @classmethod  # 声明为类方法
    def validate_type_not_empty(cls, v: str) -> str:
        """验证器：确保类型 (type) 字段不为空或仅包含空白字符。"""
        if not v or not v.strip():
            raise ValueError("前沿动态的类型 (type) 字段不能为空字符串。")
        return v.strip()

    @field_validator('hotness')
    @classmethod
    def validate_hotness(cls, v: str) -> str:
        """验证器：确保热度 (hotness) 字段必须是 '高'、'中'、'低' 之一，且不为空。"""
        allowed_values = {"高", "中", "低"}
        if v not in allowed_values:
            raise ValueError("热度 (hotness) 字段必须是 '高'、'中'、'低' 之一。")
        return v


class FrontierInsightsResult(BaseModel):
    """
    整个事件提取操作结果的数据模型。
    通常包含一个从单篇或多篇文档中提取到的事件列表，以及一些元数据。
    """
    results: List[FrontierInsightsInfo] = Field(default_factory=list, description="提取的前沿动态列表")
    total: Optional[int] = Field(default=0, description="提取到的前沿动态总数")
    time: datetime = Field(default_factory=datetime.now, description="提取操作完成的时间戳")
    content: str = Field(description="进行提取的LLM原始生成文本")

    @model_validator(mode='after')
    def set_total_results(self) -> 'FrontierInsightsResult':
        """
        模型验证器：根据 `results` 列表的长度自动计算并设置 `total` 字段的值。
        此方法在模型的所有字段被填充和验证后执行。
        """
        self.total = len(self.results)
        return self
