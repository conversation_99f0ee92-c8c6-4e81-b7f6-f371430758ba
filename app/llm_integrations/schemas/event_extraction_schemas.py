from datetime import datetime
from typing import Dict, List, Any, Optional
from pydantic import BaseModel, Field, field_validator, model_validator


class EventArgument(BaseModel):
    """
    事件论元 (Argument) 的数据模型。
    表示事件中的一个具体参与者、属性或相关信息。
    """
    role: str = Field(description="表示该论元在事件中扮演的角色 (例如: '施事者', '受事者', '地点', '时间')")
    values: List[str] = Field(default_factory=list, description="论元在原始文本中的所有具体值，可多个，列表形式存储")


class ExtractedEvent(BaseModel):
    """
    单个提取事件的数据模型。
    封装了一个从文本中识别出的具体事件的所有相关信息。
    """
    type: str = Field(description="事件的分类或类型 (例如: '产品行为-发布', '组织关系-加盟')")
    trigger: str = Field(description="在原始文本中明确指示或触发该事件识别的核心词语或短语")
    arguments: List[EventArgument] = Field(default_factory=list, description="构成该具体事件核心或描述其背景的要素论元列表")
    description: str = Field(description="对提取到的事件的一个简洁的自然语言描述")

    @field_validator('type')
    @classmethod  # 声明为类方法
    def validate_event_type_not_empty(cls, v: str) -> str:
        """验证器：确保事件类型 (event_type) 字段不为空或仅包含空白字符。"""
        if not v or not v.strip():
            raise ValueError("事件类型 (event_type) 字段不能为空字符串。")
        return v.strip()

    @field_validator('trigger')
    @classmethod  # 声明为类方法
    def validate_event_trigger_not_empty(cls, v: str) -> str:
        """验证器：确保事件触发词 (event_trigger) 字段不为空或仅包含空白字符。"""
        if not v or not v.strip():
            raise ValueError("事件触发词 (event_trigger) 字段不能为空字符串。")
        return v.strip()


class EventExtractionResult(BaseModel):
    """
    整个事件提取操作结果的数据模型。
    通常包含一个从单篇或多篇文档中提取到的事件列表，以及一些元数据。
    """
    results: List[ExtractedEvent] = Field(default_factory=list, description="提取的事件列表")
    total: Optional[int] = Field(default=0, description="提取到的事件总数")
    time: datetime = Field(default_factory=datetime.now, description="事件提取操作完成的时间戳")
    content: str = Field(description="进行事件提取的LLM原始生成文本")

    @model_validator(mode='after')
    def set_total_results(self) -> 'EventExtractionResult':
        """
        模型验证器：根据 `events` 列表的长度自动计算并设置 `total_events` 字段的值。
        此方法在模型的所有字段被填充和验证后执行。
        """
        self.total = len(self.results)
        return self
