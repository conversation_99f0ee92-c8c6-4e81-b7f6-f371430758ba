from datetime import datetime
from typing import Dict, List, Any, Optional
from pydantic import BaseModel, Field, field_validator, model_validator


class NearbyHotspotInfo(BaseModel):
    """
    单个提取周边热点事件的数据模型。
    封装了一个从文本中识别出的具体事件的所有相关信息。
    """
    influence_area: str = Field(description="明确指出事件发生或产生影响的具体国家、海域或地区。(如：中东、东北亚、越南)")
    event_summary: str = Field(description="用简洁的语言概括事件的核心内容、关键参与方和主要动态")
    threat_level: str = Field(description="评估该事件的潜在威胁，分为三个等级：高、中、低")
    threat_analysis: str = Field(default_factory=list, description="简要说明你评定威胁等级的依据。例如：是否涉及直接军事冲突风险、是否冲击关键产业链、是否引发地缘政治对抗、是否可能导致人道危机或边境不稳定等。")

    @field_validator('influence_area')
    @classmethod  # 声明为类方法
    def validate_area_not_empty(cls, v: str) -> str:
        """验证器：确保区域 (influence_area) 字段不为空或仅包含空白字符。"""
        if not v or not v.strip():
            raise ValueError("周边热点事件的区域 (influence_area) 字段不能为空字符串。")
        return v.strip()

    @field_validator('threat_level')
    @classmethod
    def validate_threat_level(cls, v: str) -> str:
        """验证器：确保威胁程度 (threat_level) 字段必须是 '高'、'中'、'低' 之一，且不为空。"""
        allowed_values = {"高", "中", "低"}
        if v not in allowed_values:
            raise ValueError("威胁程度 (threat_level) 字段必须是 '高'、'中'、'低' 之一。")
        return v


class NearbyHotspotInfoResult(BaseModel):
    """
    整个周边热点事件提取操作结果的数据模型。
    通常包含一个从单篇或多篇文档中提取到的事件列表，以及一些元数据。
    """
    results: List[NearbyHotspotInfo] = Field(default_factory=list, description="提取的周边热点事件列表")
    total: Optional[int] = Field(default=0, description="提取到的周边热点事件总数")
    time: datetime = Field(default_factory=datetime.now, description="提取操作完成的时间戳")
    content: str = Field(description="进行提取的LLM原始生成文本")

    @model_validator(mode='after')
    def set_total_results(self) -> 'NearbyHotspotInfoResult':
        """
        模型验证器：根据 `results` 列表的长度自动计算并设置 `total` 字段的值。
        此方法在模型的所有字段被填充和验证后执行。
        """
        self.total = len(self.results)
        return self
