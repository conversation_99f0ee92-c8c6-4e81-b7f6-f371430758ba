from langchain_openai import ChatOpenAI
from app.core.config import settings


class LlmModel:

    def __init__(self):
        self.model = ChatOpenAI(
            base_url=str(settings.LLM_API_BASE),
            model=settings.LLM_MODEL_NAME,
            api_key=settings.LLM_API_KEY,
            temperature=0.6,
            top_p=0.95,
            streaming=True
            # model_kwargs={"response_format": {"type": "json_object"}}
        )

    def get_openai_compatible_llm(self) -> ChatOpenAI:
        """
        获取并配置 OpenAI LLM 实例。

        Returns:
            ChatOpenAI: 配置好的 ChatOpenAI 实例。
        """
        return self.model
