import json
from typing import Dict, Any, List, Optional

from langchain_core.output_parsers import BaseOutputParser
from langchain_core.exceptions import OutputParserException
from pydantic import TypeAdapter, ValidationError
import json_repair

from ...core.logger_config import get_logger
from ...utils.json_extractor import JsonExtractor
from ..schemas.event_extraction_schemas import EventExtractionResult, ExtractedEvent, EventArgument
from ...utils.llm_output_cleaner import LLMOutputCleaner

logger = get_logger()
_llm_output_cleaner = LLMOutputCleaner(remove_tags=['think'], strip_markdown=True, normalize_whitespace=True)


class EventExtractionOutputParser(BaseOutputParser[EventExtractionResult]):
    """
    事件提取专用输出解析器。

    这个类继承自 LangChain 的 `BaseOutputParser`，其核心职责是将
    大语言模型 (LLM) 返回的原始文本输出（通常期望是 JSON 格式的字符串）
    解析、验证并转换为结构化的 `EventExtractionResult` Pydantic 模型对象。
    它处理了 JSON 清理、字段名标准化、数据结构转换等步骤。
    """

    def _clean_json_string_list(self, json_str: str) -> list[str]:
        """
        辅助方法：清理可能包含在 Markdown 代码块标记 (```json ... ``` 或 ``` ... ```)
        中的 JSON 字符串。
        Args:
            json_str (str): 可能包含代码块标记的原始 JSON 字符串。
        Returns:
            str: 清理后的 JSON 字符串。
        """
        cleaner_str = _llm_output_cleaner.clean(json_str)
        repaired_json_str_list = json_repair.loads(cleaner_str)
        if repaired_json_str_list is None:
            repaired_json_str_list = []
        logger.debug(f"事件提取输出清理后的 JSON 字符串数量: {len(repaired_json_str_list)}")
        return repaired_json_str_list if repaired_json_str_list is not None else []

    def _llm_output_load_to_json_list(self, json_str: str) -> list[dict]:
        """
        辅助方法：清理可能包含在 Markdown 代码块标记 (```json ... ``` 或 ``` ... ```)
        中的 JSON 字符串。
        Args:
            json_str (str): 可能包含代码块标记的原始 JSON 字符串。
        Returns:
            str: 清理后的 JSON 字符串。
        """
        cleaner_str = _llm_output_cleaner.clean(json_str)
        repaired_json_list = json_repair.loads(cleaner_str)
        if repaired_json_list is None:
            repaired_json_str_list = []
        logger.debug(f"事件提取输出清理后的 JSON 数量: {len(repaired_json_list)}")
        return repaired_json_list

    def parse(self, text: str) -> EventExtractionResult:
        """
        解析 LLM 输出的文本，并返回一个 `EventExtractionResult` 对象。
        这是 `BaseOutputParser` 的核心方法。

        Args:
            text (str): 从 LLM 获取的原始文本输出。
        Returns:
            EventExtractionResult: 解析和验证后的结构化事件提取结果。
        Raises:
            OutputParserException: 如果解析失败或数据不符合预期格式。
        """
        # 记录日志，开始解析过程
        logger.debug(f"正在解析 LLM 输出的文本: '{text[:1000]}...'")

        # 初始化事件数据列表为 None
        event_data_list: Optional[List[ExtractedEvent]] = None

        # 清理 JSON 字符串，为解析做准备
        cleaned_jsons = self._llm_output_load_to_json_list(text)

        # 如果没有找到任何有效的 JSON 字符串，则抛出异常
        if not cleaned_jsons:
            error_msg = "没有找到任何有效的 JSON 字符串。"
            raise OutputParserException(error_msg, llm_output=text)
        try:
            logger.debug(f"尝试将所有清理后的 JSON 字符串一次性解析为事件数据列表。")
            event_data_list = TypeAdapter(List[ExtractedEvent]).validate_python(cleaned_jsons)
        except ValidationError as e:
            logger.warning(f"一次性解析数据验证失败: {e.errors()}. 将尝试逐个解析。")
        except Exception as e:
            logger.warning(f"一次性解析数据，发生未知错误: {e}. 将尝试逐个解析。")

        if event_data_list is None:
            # 遍历每个清理后的 JSON 字符串，尝试解析
            for i, cleaned_json in enumerate(cleaned_jsons):
                logger.debug(f"正在处理第 {i + 1} 个清理后的 JSON 字符串 (共 {len(cleaned_jsons)} 个)")

                # 尝试解析 JSON 并提取事件数据
                try:
                    event_temp_list: List[ExtractedEvent] = TypeAdapter(List[ExtractedEvent]).validate_python(cleaned_json)
                    if event_temp_list:
                        event_data_list = event_temp_list
                        logger.debug(
                            f"第 {i + 1} 个清理后的 JSON 字符串解析成功，共 {len(event_data_list)} 个事件。跳过后续的解析。")
                        break
                    else:
                        logger.debug(
                            f"第 {i + 1} 个清理后的 JSON 字符串解析成功，但未包含任何事件数据。继续处理下一个 JSON 块。")
                except json.JSONDecodeError as e:
                    logger.warning(f"处理第 {i + 1} 个清理后的 JSON 字符串时，JSON 解析失败: {e.msg}. 跳过此 JSON 块。")
                    continue
                except ValidationError as e:
                    logger.warning(f"处理第 {i + 1} 个清理后的 JSON 字符串时，数据验证失败: {e.errors()}. 跳过此 JSON 块。")
                    continue
                except Exception as e:
                    logger.warning(f"处理第 {i + 1} 个清理后的 JSON 字符串时，发生未知错误: {e}. 跳过此 JSON 块。")
                    continue

        # 如果所有 JSON 块都未能解析为事件数据，则抛出异常
        if event_data_list is None:
            error_msg = "所有清理后的 JSON 块都未能成功解析为事件数据。"
            raise OutputParserException(error_msg, llm_output=text)

        # 创建并返回事件提取结果对象
        ee_result = EventExtractionResult(content=text, results=event_data_list)
        return ee_result

    @property
    def _type(self) -> str:
        """LangChain 要求所有输出解析器提供此属性，用于标识解析器类型。"""
        return "event_extraction_output_parser"
