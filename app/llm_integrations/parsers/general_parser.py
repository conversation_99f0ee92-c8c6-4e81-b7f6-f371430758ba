import json
from typing import Dict, Any, List, Optional

from langchain_core.output_parsers import BaseOutputParser
from langchain_core.exceptions import OutputParserException
from pydantic import TypeAdapter, ValidationError
import json_repair

from ...core.logger_config import get_logger
from ...utils.json_extractor import JsonExtractor
from ..schemas.general_schemas import GeneralInfoResult
from ...utils.llm_output_cleaner import LLMOutputCleaner

logger = get_logger()
_llm_output_cleaner = LLMOutputCleaner(remove_tags=['think'], strip_markdown=True, normalize_whitespace=True)


class GeneralOutputParser(BaseOutputParser[GeneralInfoResult]):
    """
    通用LLM输出解析器。

    这个类继承自 LangChain 的 `BaseOutputParser`，其核心职责是将
    大语言模型 (LLM) 返回的原始文本输出解析、验证并转换为结构化的 
    `GeneralInfoResult` Pydantic 模型对象。

    与特定领域的解析器不同，这个通用解析器专注于处理任意格式的LLM输出，
    不对输出内容的结构做特定假设，而是将清理后的文本直接封装到结果对象中。
    """

    def _clean_and_extract_content(self, text: str) -> str:
        """
        清理LLM输出文本，移除不必要的标记和格式。

        Args:
            text (str): 从LLM获取的原始文本输出。

        Returns:
            str: 清理后的文本内容。
        """
        try:
            # 使用LLMOutputCleaner进行基础清理
            cleaned_text = _llm_output_cleaner.clean(text)
            
            # 尝试提取JSON内容（如果存在）
            try:
                # 尝试解析为JSON，如果成功则格式化输出
                json_data = json_repair.loads(cleaned_text)
                if json_data is not None:
                    # 如果是有效的JSON，格式化后返回
                    return json.dumps(json_data, ensure_ascii=False, indent=2)
            except (json.JSONDecodeError, ValueError):
                # 如果不是JSON格式，继续使用清理后的文本
                pass
            
            # 进一步清理：移除多余的空行，但保留基本格式
            lines = cleaned_text.split('\n')
            cleaned_lines = []
            prev_empty = False
            
            for line in lines:
                line = line.strip()
                if line:
                    cleaned_lines.append(line)
                    prev_empty = False
                elif not prev_empty:
                    # 只保留一个空行
                    cleaned_lines.append('')
                    prev_empty = True
            
            # 移除开头和结尾的空行
            while cleaned_lines and not cleaned_lines[0]:
                cleaned_lines.pop(0)
            while cleaned_lines and not cleaned_lines[-1]:
                cleaned_lines.pop()
            
            result = '\n'.join(cleaned_lines)
            logger.debug(f"通用解析器清理后的内容长度: {len(result)}")
            return result
            
        except Exception as e:
            logger.warning(f"清理LLM输出时发生错误: {str(e)}，返回原始文本")
            return text.strip()

    def _try_extract_structured_data(self, text: str) -> Optional[Dict[str, Any]]:
        """
        尝试从文本中提取结构化数据（JSON）。

        Args:
            text (str): 清理后的文本。

        Returns:
            Optional[Dict[str, Any]]: 如果找到有效的JSON数据则返回，否则返回None。
        """
        try:
            # 尝试直接解析整个文本
            json_data = json_repair.loads(text)
            if isinstance(json_data, (dict, list)):
                logger.debug("成功提取结构化数据")
                return json_data
        except (json.JSONDecodeError, ValueError):
            pass
        
        # 尝试使用JsonExtractor提取JSON片段
        try:
            extractor = JsonExtractor()
            json_strings = extractor.extract_json_strings(text)
            
            if json_strings:
                # 尝试解析第一个有效的JSON字符串
                for json_str in json_strings:
                    try:
                        json_data = json.loads(json_str)
                        if json_data:
                            logger.debug("通过JsonExtractor成功提取结构化数据")
                            return json_data
                    except json.JSONDecodeError:
                        continue
        except Exception as e:
            logger.debug(f"JsonExtractor提取失败: {str(e)}")
        
        return None

    def parse(self, text: str) -> GeneralInfoResult:
        """
        解析 LLM 输出的文本，并返回一个 `GeneralInfoResult` 对象。
        这是 `BaseOutputParser` 的核心方法。

        Args:
            text (str): 从 LLM 获取的原始文本输出。

        Returns:
            GeneralInfoResult: 解析后的通用结果对象。

        Raises:
            OutputParserException: 如果解析失败或文本为空。
        """
        # 记录日志，开始解析过程
        logger.debug(f"正在解析通用LLM输出的文本: '{text[:500]}...'")

        # 验证输入
        if not text or not text.strip():
            error_msg = "LLM输出文本为空或仅包含空白字符。"
            raise OutputParserException(error_msg, llm_output=text)

        try:
            # 清理和提取内容
            cleaned_content = self._clean_and_extract_content(text)
            
            # 验证清理后的内容
            if not cleaned_content or not cleaned_content.strip():
                logger.warning("清理后的内容为空，使用原始文本")
                cleaned_content = text.strip()

            # 尝试提取结构化数据（可选）
            structured_data = self._try_extract_structured_data(cleaned_content)
            
            # 创建并返回结果对象
            result = GeneralInfoResult(content=cleaned_content)
            
            # 如果提取到结构化数据，可以在日志中记录
            if structured_data:
                logger.debug(f"提取到结构化数据类型: {type(structured_data).__name__}")
            
            logger.debug(f"通用解析器解析成功，内容长度: {len(cleaned_content)}")
            return result

        except Exception as e:
            error_msg = f"通用解析器解析失败: {str(e)}"
            logger.error(error_msg)
            raise OutputParserException(error_msg, llm_output=text)

    @property
    def _type(self) -> str:
        """LangChain 要求所有输出解析器提供此属性，用于标识解析器类型。"""
        return "general_output_parser"
