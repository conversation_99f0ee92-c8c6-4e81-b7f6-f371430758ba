from typing import Dict, Any, Optional
from ...core.logger_config import get_logger
from .base_callback import BaseChainCallback

logger = get_logger()


class FrontierInsightsCallback(BaseChainCallback):
    """
    前沿动态提取专用回调处理器。
    继承自 BaseChainCallback，并添加了前沿动态提取任务特有的统计项
    （如已提取前沿动态总数）和可能的特定日志记录逻辑。
    """

    def __init__(self):
        # 调用父类构造函数，并指定此回调对应的链名称
        super().__init__(chain_name="FrontierInsightsChain")

        # 添加前沿动态提取特有的统计项
        # total_insights_extracted 记录的是经过后处理验证的前沿动态总数
        self.stats['total_insights_extracted'] = 0

    def on_chain_end(
            self,
            outputs: Dict[str, Any],
            *,
            run_id: Any,
            inputs: Optional[Dict[str, Any]] = None,
            **kwargs: Any,
    ) -> None:
        # 首先调用父类的 on_chain_end 方法处理通用逻辑（如计时、通用日志）
        super().on_chain_end(outputs, run_id=run_id, inputs=inputs, **kwargs)

        # 添加前沿动态提取特有的日志逻辑
        num_insights_in_current_output = 0
        output_data = outputs
        if isinstance(outputs, dict):
            if 'result' in outputs:
                output_data = outputs['result']
            elif 'output' in outputs:
                output_data = outputs['output']

        if hasattr(output_data, 'results') and isinstance(getattr(output_data, 'results', None), list):
            num_insights_in_current_output = len(getattr(output_data, 'results'))

        logger.info(
            f"回调 (FrontierInsightsChain | Run ID: {run_id}): 前沿动态提取链结束。"
            f"本次输出前沿动态数(过滤前): {num_insights_in_current_output}. "
            f"累计已确认提取前沿动态总数: {self.stats.get('total_insights_extracted', 0)}"
        )

    def update_extracted_insights_count(self, count: int) -> None:
        """
        由 FrontierInsightsChain 在其后处理逻辑中调用，
        用于更新已确认提取并经过验证的前沿动态数量。
        """
        self.stats['total_insights_extracted'] += count
        logger.debug(
            f"回调 (FrontierInsightsChain): 更新已提取前沿动态数统计，本次新增 {count}，"
            f"当前累计总数: {self.stats['total_insights_extracted']}"
        )
