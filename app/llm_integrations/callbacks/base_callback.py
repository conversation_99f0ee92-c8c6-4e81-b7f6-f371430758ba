import time
from typing import Dict, Any, Union, Optional, List
from langchain_core.callbacks import BaseCallbackHandler
from langchain_core.outputs import LLMResult
from ...core.logger_config import get_logger

logger = get_logger()


class BaseChainCallback(BaseCallbackHandler):
    """
    通用的链回调处理器。
    实现了 BaseCallbackHandler 接口，并补全了 LLM 相关的具体回调方法，
    以提供更详细、更具可读性的日志和统计。
    """

    def __init__(self, chain_name: str = "Unknown<PERSON>hain"):
        """构造函数。"""
        super().__init__()
        self.chain_name = chain_name
        self.stats: Dict[str, Any] = {
            'total_requests': 0,
            'successful_requests': 0,
            'failed_requests': 0,
            'retry_count': 0,  # 用于记录 Tenacity 等重试库的重试次数
            'total_processing_time_ms': 0.0,  # 总处理时间
            'average_processing_time_ms': 0.0,  # 平均处理时间
            'total_llm_calls': 0,  # LLM 调用总次数
            'total_llm_errors': 0,  # LLM 调用失败次数
            'total_llm_tokens_generated': 0,  # LLM 生成的总 token 数
            'total_llm_processing_time_ms': 0.0,  # LLM 总处理时间
        }
        # 存储每个运行单元（包括链和LLM）的开始时间
        self._request_start_times: Dict[str, float] = {}
        # 存储最外层链的 run_id，以便区分
        self._outermost_run_id: Optional[str] = None

    def _current_time_ms(self) -> float:
        """获取当前时间的毫秒数。"""
        return time.time() * 1000

    def on_chain_start(
            self,
            serialized: Dict[str, Any],
            inputs: Dict[str, Any],
            *,
            run_id: Any,
            **kwargs: Any,
    ) -> None:
        """当一个链或链中的一个组件开始执行时调用。"""
        # 将第一个遇到的 run_id 视为最外层链的 ID
        if self._outermost_run_id is None:
            self._outermost_run_id = str(run_id)
            self.stats['total_requests'] += 1
            logger.info(
                f"回调 ({self.chain_name} | Run ID: {run_id}): [CHAIN START] 最外层链开始执行。"
            )
            self._request_start_times[str(run_id)] = self._current_time_ms()

        # 为所有子组件记录开始日志
        logger.debug(
            f"回调 ({self.chain_name} | Run ID: {run_id}): [COMPONENT START] 组件开始执行。"
        )

    def on_chain_end(
            self,
            outputs: Dict[str, Any],
            *,
            run_id: Any,
            **kwargs: Any,
    ) -> None:
        """当一个链或链中的一个组件成功结束时调用。"""
        run_id_str = str(run_id)
        logger.debug(
            f"回调 ({self.chain_name} | Run ID: {run_id}): [COMPONENT END] 组件成功结束。"
        )

        # 只计算和记录最外层链的结束和总时长
        if run_id_str == self._outermost_run_id:
            self.stats['successful_requests'] += 1
            if run_id_str in self._request_start_times:
                start_time = self._request_start_times.pop(run_id_str)
                duration_ms = self._current_time_ms() - start_time
                self.stats['total_processing_time_ms'] += duration_ms
                if self.stats['successful_requests'] > 0:
                    self.stats['average_processing_time_ms'] = \
                        self.stats['total_processing_time_ms'] / self.stats['successful_requests']

                output_summary = str(outputs)[:1000] + "..." if len(str(outputs)) > 1000 else str(outputs)
                logger.info(
                    f"回调 ({self.chain_name} | Run ID: {run_id}): [CHAIN END] 最外层链成功结束。"
                    f"总处理时长: {duration_ms:.2f} ms. 输出摘要: {output_summary}"
                )
            # 任务结束后重置最外层 run_id
            self._outermost_run_id = None

    def on_chain_error(
            self,
            error: Union[Exception, KeyboardInterrupt],
            *,
            run_id: Any,
            **kwargs: Any,
    ) -> None:
        """当一个链或链中的一个组件执行失败时调用。"""
        logger.error(
            f"回调 ({self.chain_name} | Run ID: {run_id}): [COMPONENT ERROR] 组件执行失败。错误: {error}"
        )

        run_id_str = str(run_id)
        if run_id_str == self._outermost_run_id:
            self.stats['failed_requests'] += 1
            if run_id_str in self._request_start_times:
                self._request_start_times.pop(run_id_str)
            logger.error(
                f"回调 ({self.chain_name} | Run ID: {run_id}): [CHAIN ERROR] 最外层链执行失败。错误: {error}"
            )
            # 任务结束后重置最外层 run_id
            self._outermost_run_id = None

    def on_llm_start(
            self,
            serialized: Dict[str, Any],
            prompts: List[str],
            *,
            run_id: Any,
            **kwargs: Any,
    ) -> None:
        """当 LLM 调用开始时调用。"""
        self.stats['total_llm_calls'] += 1
        llm_run_id_str = f"llm_{run_id}"
        self._request_start_times[llm_run_id_str] = self._current_time_ms()  # 为 LLM 调用单独计时

        prompt_summary = prompts[0][:150] + "..." if prompts and len(prompts[0]) > 150 else (
            prompts[0] if prompts else "无")
        logger.info(
            f"回调 ({self.chain_name} | LLM Run ID: {run_id}): [LLM START] 组件开始执行。Prompt 摘要: {prompt_summary}"
        )

    def on_llm_end(
            self,
            response: LLMResult,
            *,
            run_id: Any,
            **kwargs: Any,
    ) -> None:
        """当 LLM 调用成功结束时调用。"""
        llm_run_id_str = f"llm_{run_id}"

        if llm_run_id_str in self._request_start_times:
            start_time = self._request_start_times.pop(llm_run_id_str)
            duration_ms = self._current_time_ms() - start_time
            self.stats['total_llm_processing_time_ms'] += duration_ms

            # 统计 token 使用情况
            token_usage = response.llm_output.get('token_usage', {}) if response.llm_output else {}
            completion_tokens = token_usage.get('completion_tokens', 0)
            self.stats['total_llm_tokens_generated'] += completion_tokens

            logger.info(
                f"回调 ({self.chain_name} | LLM Run ID: {run_id}): [LLM END] 组件成功结束。"
                f"处理时长: {duration_ms:.2f} ms. 生成 Tokens: {completion_tokens}."
            )

    def on_llm_error(
            self,
            error: Union[Exception, KeyboardInterrupt],
            *,
            run_id: Any,
            **kwargs: Any,
    ) -> None:
        """当 LLM 调用失败时调用。"""
        self.stats['total_llm_errors'] += 1
        llm_run_id_str = f"llm_{run_id}"

        if llm_run_id_str in self._request_start_times:
            self._request_start_times.pop(llm_run_id_str)
        logger.error(
            f"回调 ({self.chain_name} | LLM Run ID: {run_id}): [LLM ERROR] 组件调用失败。错误: {error}"
        )

    def custom_on_tenacity_retry(self, retry_state: Any) -> None:
        """自定义方法，用于被 Tenacity 的 @retry 装饰器的回调钩子调用。"""
        self.stats['retry_count'] += 1
        attempt_number = getattr(retry_state, 'attempt_number', 'N/A')
        exception_info = None
        if retry_state.outcome:
            try:
                exception_info = retry_state.outcome.exception()
            except Exception:
                exception_info = "无法获取异常信息"

        logger.warning(
            f"回调 ({self.chain_name}): Tenacity 正在重试，尝试次数 {attempt_number}。"
            f"上一次失败原因: {exception_info if exception_info else '未知'}"
        )

    def get_stats(self) -> Dict[str, Any]:
        """获取当前回调处理器的所有统计信息。"""
        # 更新衍生统计
        if self.stats['successful_requests'] > 0:
            self.stats['average_processing_time_ms'] = \
                self.stats['total_processing_time_ms'] / self.stats['successful_requests']
        else:
            self.stats['average_processing_time_ms'] = 0.0

        if self.stats['total_llm_calls'] > 0:
            self.stats['average_llm_processing_time_ms'] = \
                self.stats['total_llm_processing_time_ms'] / self.stats['total_llm_calls']
        else:
            self.stats['average_llm_processing_time_ms'] = 0.0

        return self.stats.copy()
