from typing import Dict, Any, Optional
from ...core.logger_config import get_logger
from .base_callback import BaseChainCallback

logger = get_logger()


class GeneralCallback(BaseChainCallback):
    """
    通用LLM调用专用回调处理器。
    
    继承自 BaseChainCallback，并添加了通用LLM调用任务特有的统计项
    和监控功能。这个回调处理器适用于任何类型的LLM调用，不限定于
    特定的业务领域。
    """

    def __init__(self):
        # 调用父类构造函数，并指定此回调对应的链名称
        super().__init__(chain_name="GeneralChain")

        # 添加通用LLM调用特有的统计项
        self.stats.update({
            'total_content_processed': 0,  # 处理的总内容数量
            'total_content_length': 0,     # 处理的总内容长度（字符数）
            'average_content_length': 0.0, # 平均内容长度
            'total_map_operations': 0,     # Map操作总次数
            'total_combine_operations': 0, # Combine操作总次数
            'total_single_operations': 0,  # 单次处理操作总次数
            'map_reduce_usage_rate': 0.0,  # Map-Reduce模式使用率
            'content_size_distribution': { # 内容大小分布统计
                'small': 0,    # 小文本 (< 2000字符)
                'medium': 0,   # 中等文本 (2000-8000字符)
                'large': 0,    # 大文本 (> 8000字符)
            },
            'processing_mode_stats': {     # 处理模式统计
                'single_mode': 0,          # 单次处理模式次数
                'map_reduce_mode': 0,      # Map-Reduce模式次数
            }
        })

    def on_chain_start(
            self,
            serialized: Dict[str, Any],
            inputs: Dict[str, Any],
            *,
            run_id: Any,
            **kwargs: Any,
    ) -> None:
        """当链开始执行时调用，记录输入内容的统计信息。"""
        # 调用父类方法处理通用逻辑
        super().on_chain_start(serialized, inputs, run_id=run_id, **kwargs)

        # 分析输入内容
        if isinstance(inputs, dict) and 'text' in inputs:
            content = inputs['text']
            content_length = len(content) if content else 0
            
            # 更新内容统计
            self.stats['total_content_processed'] += 1
            self.stats['total_content_length'] += content_length
            
            # 计算平均内容长度
            if self.stats['total_content_processed'] > 0:
                self.stats['average_content_length'] = (
                    self.stats['total_content_length'] / self.stats['total_content_processed']
                )
            
            # 更新内容大小分布
            if content_length < 2000:
                self.stats['content_size_distribution']['small'] += 1
            elif content_length <= 8000:
                self.stats['content_size_distribution']['medium'] += 1
            else:
                self.stats['content_size_distribution']['large'] += 1
            
            logger.debug(
                f"回调 (GeneralChain | Run ID: {run_id}): 开始处理内容，"
                f"长度: {content_length} 字符"
            )

    def on_chain_end(
            self,
            outputs: Dict[str, Any],
            *,
            run_id: Any,
            inputs: Optional[Dict[str, Any]] = None,
            **kwargs: Any,
    ) -> None:
        """当链成功结束时调用，记录输出结果的统计信息。"""
        # 首先调用父类的 on_chain_end 方法处理通用逻辑（如计时、通用日志）
        super().on_chain_end(outputs, run_id=run_id, inputs=inputs, **kwargs)

        # 分析输出结果
        output_content_length = 0
        if isinstance(outputs, dict):
            # 尝试从不同可能的输出结构中提取内容
            output_data = outputs
            if 'result' in outputs:
                output_data = outputs['result']
            elif 'output' in outputs:
                output_data = outputs['output']
            
            # 如果输出是GeneralInfoResult对象
            if hasattr(output_data, 'content'):
                content = getattr(output_data, 'content', '')
                output_content_length = len(content) if content else 0

        # 计算Map-Reduce使用率
        total_operations = (self.stats['total_map_operations'] + 
                          self.stats['total_combine_operations'] + 
                          self.stats['total_single_operations'])
        
        if total_operations > 0:
            map_reduce_operations = self.stats['total_map_operations'] + self.stats['total_combine_operations']
            self.stats['map_reduce_usage_rate'] = (map_reduce_operations / total_operations) * 100

        logger.info(
            f"回调 (GeneralChain | Run ID: {run_id}): 通用LLM调用链结束。"
            f"输出内容长度: {output_content_length} 字符. "
            f"累计处理内容数: {self.stats.get('total_content_processed', 0)}. "
            f"Map-Reduce使用率: {self.stats.get('map_reduce_usage_rate', 0):.1f}%"
        )

    def update_processing_mode(self, mode: str) -> None:
        """
        更新处理模式统计。
        
        Args:
            mode: 处理模式，'single' 或 'map_reduce'
        """
        if mode == 'single':
            self.stats['total_single_operations'] += 1
            self.stats['processing_mode_stats']['single_mode'] += 1
            logger.debug("回调 (GeneralChain): 记录单次处理模式")
        elif mode == 'map_reduce':
            self.stats['processing_mode_stats']['map_reduce_mode'] += 1
            logger.debug("回调 (GeneralChain): 记录Map-Reduce处理模式")

    def update_map_operation(self) -> None:
        """记录一次Map操作。"""
        self.stats['total_map_operations'] += 1
        logger.debug(
            f"回调 (GeneralChain): 记录Map操作，"
            f"当前总计: {self.stats['total_map_operations']}"
        )

    def update_combine_operation(self) -> None:
        """记录一次Combine操作。"""
        self.stats['total_combine_operations'] += 1
        logger.debug(
            f"回调 (GeneralChain): 记录Combine操作，"
            f"当前总计: {self.stats['total_combine_operations']}"
        )

    def get_processing_summary(self) -> Dict[str, Any]:
        """
        获取处理统计摘要。
        
        Returns:
            Dict[str, Any]: 包含各种统计信息的字典
        """
        return {
            'total_requests': self.stats['total_requests'],
            'successful_requests': self.stats['successful_requests'],
            'failed_requests': self.stats['failed_requests'],
            'total_content_processed': self.stats['total_content_processed'],
            'average_content_length': round(self.stats['average_content_length'], 2),
            'map_reduce_usage_rate': round(self.stats['map_reduce_usage_rate'], 2),
            'content_size_distribution': self.stats['content_size_distribution'].copy(),
            'processing_mode_stats': self.stats['processing_mode_stats'].copy(),
            'average_processing_time_ms': round(self.stats['average_processing_time_ms'], 2),
            'total_llm_calls': self.stats['total_llm_calls'],
            'total_llm_tokens_generated': self.stats['total_llm_tokens_generated'],
        }
