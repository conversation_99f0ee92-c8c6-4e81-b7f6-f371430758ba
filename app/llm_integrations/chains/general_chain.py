import asyncio
from typing import Dict, Any, List, Optional, Union

from langchain_core.language_models import BaseLanguageModel
from langchain_core.prompts import BasePromptTemplate
from langchain_core.output_parsers import BaseOutputParser
from langchain_core.runnables import Runnable, RunnablePassthrough, RunnableConfig
from langchain_text_splitters import RecursiveCharacterTextSplitter
from tenacity import retry, stop_after_attempt, wait_exponential, retry_if_exception_type
from langchain_core.exceptions import OutputParserException
import json

from ..llm_models import LlmModel
from ..prompts.general_prompt import map_prompt, combine_prompt
from ...core.logger_config import get_logger
from ..schemas.general_schemas import GeneralInfoResult
from ..parsers.general_parser import GeneralOutputParser
from ..callbacks.general_callback import GeneralCallback
from ..configs.general_config import GeneralConfig

logger = get_logger()


class GeneralChain:
    """
    通用LLM调用链。

    这个类封装了一个完整的通用LLM调用流水线，包括：
    - LLM 模型调用
    - 动态Prompt模板应用
    - 输出解析和验证
    - 回调处理
    - 错误处理和重试机制
    - Map-Reduce模式支持（用于处理长文本）

    它使用 LangChain 的 LCEL (LangChain Expression Language) 来构建处理链，
    并提供同步和异步两种调用方式。支持通过prompt_variables动态注入提示内容。
    """

    def __init__(
            self,
            llm: BaseLanguageModel,
            prompt: Optional[BasePromptTemplate] = None,
            output_parser: Optional[BaseOutputParser] = None,
            callback_handler: Optional[GeneralCallback] = None,
            config: Optional[GeneralConfig] = None
    ):
        """
        初始化通用LLM调用链。

        Args:
            llm: 用于处理的语言模型实例
            prompt: 可选的 Prompt 模板，如果未提供则使用默认的 map_prompt
            output_parser: 可选的输出解析器，如果未提供则使用默认的 GeneralOutputParser
            callback_handler: 可选的回调处理器，如果未提供则使用默认的 GeneralCallback
            config: 可选的配置对象，如果未提供则使用默认的 GeneralConfig
        """
        if prompt is None:
            raise ValueError("参数 'prompt' (BasePromptTemplate实例) 不能为空。")
        self.prompt: BasePromptTemplate = prompt or map_prompt

        if llm is None:
            raise ValueError("参数 'llm' (BaseLanguageModel实例) 不能为空。")
        self.llm: BaseLanguageModel = llm

        self.output_parser: BaseOutputParser[GeneralInfoResult] = output_parser or GeneralOutputParser()

        self.callback_handler: Optional[GeneralCallback] = None
        if config is None:
            config = GeneralConfig()
        self.config: GeneralConfig = config
        
        if self.config.enable_callbacks:
            self.callback_handler = callback_handler or GeneralCallback()

        self.chain: Runnable[Dict, GeneralInfoResult] = self._build_chain()

        logger.info(
            f"GeneralChain 初始化完成。LLM: {type(self.llm).__name__}, "
            f"Prompt: {type(self.prompt).__name__}, 配置: {self.config}"
        )

    def _build_chain(self) -> Runnable[Dict, GeneralInfoResult]:
        """构建核心的LCEL处理链。"""
        return (
                RunnablePassthrough()
                | self.prompt
                | self.llm
                | self.output_parser
        )

    def _get_run_config(self) -> RunnableConfig:
        """获取运行时配置，包括回调处理器等。"""
        return RunnableConfig(
            callbacks=[self.callback_handler] if self.callback_handler else [],
            tags=["general_llm", "llm_processing"],
            metadata={"chain_type": "general_llm"}
        )

    def _get_tenacity_retry_decorator(self):
        """获取Tenacity重试装饰器，用于处理LLM调用的瞬时错误。"""
        return retry(
            stop=stop_after_attempt(self.config.max_retry_attempts),
            wait=wait_exponential(
                multiplier=1,
                min=self.config.retry_wait_min,
                max=self.config.retry_wait_max
            ),
            retry=retry_if_exception_type((
                OutputParserException,
                json.JSONDecodeError,
                asyncio.TimeoutError,
                ConnectionError
            )),
            after=self._on_retry_callback if self.callback_handler else None
        )

    def _on_retry_callback(self, retry_state):
        """Tenacity重试回调函数。"""
        if self.callback_handler:
            self.callback_handler.custom_on_tenacity_retry(retry_state)

    def _validate_input(self, text: str) -> None:
        """验证输入文本的有效性。"""
        if not text or not text.strip():
            raise ValueError("输入文本不能为空或仅包含空白字符")

        if len(text) > 1000000:  # 1MB限制
            raise ValueError(f"输入文本过长 ({len(text)} 字符)，超过最大限制 (1,000,000 字符)")

    def _calculate_prompt_overhead(self, text: str, prompt_variables: Optional[Dict[str, Any]] = None) -> int:
        """
        计算prompt的开销长度，用于确定是否需要使用Map-Reduce模式。

        Args:
            text: 输入文本
            prompt_variables: 动态prompt变量

        Returns:
            int: prompt的估计开销长度
        """
        try:
            # 准备测试变量
            test_variables = {"text": ""}  # 空文本用于测试
            if prompt_variables:
                test_variables.update(prompt_variables)

            # 格式化prompt以计算开销
            formatted_prompt = self.prompt.format(**test_variables)
            overhead = len(formatted_prompt)

            logger.debug(f"计算得到的prompt开销: {overhead} 字符")
            return overhead

        except Exception as e:
            logger.warning(f"计算prompt开销时发生错误: {str(e)}，使用默认值1000")
            return 1000  # 默认开销

    def _split_text(self, text: str, chunk_size: int) -> List[str]:
        """
        将长文本分割成适合处理的小块。

        Args:
            text: 需要分割的文本
            chunk_size: 每个块的最大大小

        Returns:
            List[str]: 分割后的文本块列表
        """
        try:
            text_splitter = RecursiveCharacterTextSplitter(
                chunk_size=chunk_size,
                chunk_overlap=self.config.chunk_overlap,
                length_function=len,
                separators=["\n\n", "\n", "。", "！", "？", "；", " ", ""]
            )

            chunks = text_splitter.split_text(text)
            logger.info(f"文本分割完成，共 {len(chunks)} 个块，块大小限制: {chunk_size}")
            return chunks

        except Exception as e:
            logger.error(f"文本分割失败: {str(e)}")
            # 如果分割失败，返回原文本作为单个块
            return [text]

    def _post_process_result(self, result: GeneralInfoResult, input_text: str) -> GeneralInfoResult:
        """
        对处理结果进行后处理。

        Args:
            result: 原始的处理结果
            input_text: 输入的原始文本

        Returns:
            GeneralInfoResult: 经过后处理的结果
        """
        if not result:
            logger.warning("处理结果为空")
            return GeneralInfoResult(content="")

        # 验证结果内容
        if not result.content or not result.content.strip():
            logger.warning("处理结果内容为空，使用输入文本的摘要")
            # 创建一个简单的摘要
            summary = input_text[:500] + "..." if len(input_text) > 500 else input_text
            result.content = f"处理完成。输入文本长度: {len(input_text)} 字符。内容摘要: {summary}"

        logger.debug(f"后处理完成，结果内容长度: {len(result.content)}")
        return result

    def _map_process_text(self, text_chunk: str, prompt_variables: Optional[Dict[str, Any]] = None) -> str:
        """
        Map阶段：处理单个文本块，返回处理结果字符串

        Args:
            text_chunk: 单个文本块
            prompt_variables: 可选的，用于格式化prompt的额外变量

        Returns:
            str: 处理后的文本内容
        """
        try:
            logger.debug(f"Map阶段处理文本块，长度: {len(text_chunk)}")

            # 构建map阶段的处理链
            map_chain = (
                RunnablePassthrough()
                | map_prompt
                | self.llm
            )

            # 准备输入变量
            input_payload = {"text": text_chunk}
            if prompt_variables:
                input_payload.update(prompt_variables)

            # 执行map处理
            run_config = self._get_run_config()
            result = map_chain.invoke(input_payload, config=run_config)

            # 提取内容
            if hasattr(result, 'content'):
                content = result.content
            else:
                content = str(result)

            # 更新回调统计
            if self.callback_handler:
                self.callback_handler.update_map_operation()

            logger.debug(f"Map阶段完成，返回内容长度: {len(content)}")
            return content

        except Exception as e:
            logger.error(f"Map阶段处理失败: {str(e)}")
            raise

    def _combine_results(self, map_results: List[str], prompt_variables: Optional[Dict[str, Any]] = None) -> GeneralInfoResult:
        """
        Combine阶段：合并多个map结果

        Args:
            map_results: Map阶段的结果列表
            prompt_variables: 可选的，用于格式化prompt的额外变量

        Returns:
            GeneralInfoResult: 合并后的处理结果
        """
        try:
            logger.debug(f"Combine阶段开始，处理 {len(map_results)} 个map结果")

            # 过滤空结果
            valid_results = [result for result in map_results if result.strip()]

            if not valid_results:
                logger.warning("所有map结果都为空，返回空结果")
                return GeneralInfoResult(content="")

            # 合并所有map结果
            combined_text = "\n---\n".join(valid_results)

            # 构建combine阶段的处理链
            combine_chain = (
                RunnablePassthrough()
                | combine_prompt
                | self.llm
                | self.output_parser
            )

            # 准备输入变量
            input_payload = {"text": combined_text}
            if prompt_variables:
                input_payload.update(prompt_variables)

            # 执行combine处理
            run_config = self._get_run_config()
            result = combine_chain.invoke(input_payload, config=run_config)

            # 更新回调统计
            if self.callback_handler:
                self.callback_handler.update_combine_operation()

            logger.debug(f"Combine阶段完成")
            return result

        except Exception as e:
            logger.error(f"Combine阶段处理失败: {str(e)}")
            raise

    def process_text(self, text: str, prompt_variables: Optional[Dict[str, Any]] = None) -> GeneralInfoResult:
        """
        同步处理给定文本，自动选择单次处理或Map-Reduce模式

        Args:
            text: 需要处理的文本
            prompt_variables: 可选的，用于格式化prompt的额外变量

        Returns:
            GeneralInfoResult: 处理结果
        """
        self._validate_input(text)

        # 计算prompt的实际开销
        prompt_overhead = self._calculate_prompt_overhead(text, prompt_variables)

        # 计算实际的文本长度阈值
        actual_threshold = self.config.chunk_size - prompt_overhead

        # 检查阈值是否有效
        if actual_threshold <= 0:
            raise ValueError(
                f"prompt开销({prompt_overhead})超过或等于chunk_size({self.config.chunk_size})，"
                f"无法进行文本处理。请增大chunk_size配置。"
            )

        # 判断是否需要使用Map-Reduce模式
        text_length = len(text)
        use_map_reduce = (
            self.config.enable_map_reduce and
            text_length > actual_threshold
        )

        if use_map_reduce:
            logger.info(f"文本长度 {text_length} 超过实际阈值 {actual_threshold}，使用Map-Reduce模式")
            if self.callback_handler:
                self.callback_handler.update_processing_mode('map_reduce')
            return self._process_text_map_reduce(text, actual_threshold, prompt_variables)
        else:
            logger.info(f"文本长度 {text_length} 未超过实际阈值 {actual_threshold}，使用单次处理模式")
            if self.callback_handler:
                self.callback_handler.update_processing_mode('single')
            return self._process_text_single(text, prompt_variables)

    def _process_text_single(self, text: str, prompt_variables: Optional[Dict[str, Any]] = None) -> GeneralInfoResult:
        """
        单次处理模式：直接处理整个文本

        Args:
            text: 需要处理的文本
            prompt_variables: 可选的，用于格式化prompt的额外变量

        Returns:
            GeneralInfoResult: 处理结果
        """
        retry_decorator = self._get_tenacity_retry_decorator()

        @retry_decorator
        def _invoke_chain_with_retry() -> GeneralInfoResult:
            logger.debug(f"单次模式处理文本，长度: {len(text)}")
            run_config = self._get_run_config()

            # 准备输入变量
            input_payload = {"text": text}
            if prompt_variables:
                input_payload.update(prompt_variables)

            raw_result: GeneralInfoResult = self.chain.invoke(input_payload, config=run_config)
            return self._post_process_result(raw_result, input_text=text)

        try:
            final_result = _invoke_chain_with_retry()
            logger.info(f"单次模式处理成功")
            return final_result
        except Exception as e:
            logger.error(f"单次模式处理失败: {str(e)}")
            raise

    def _process_text_map_reduce(self, text: str, actual_threshold: int, prompt_variables: Optional[Dict[str, Any]] = None) -> GeneralInfoResult:
        """
        Map-Reduce模式：分块处理长文本

        Args:
            text: 需要处理的文本
            actual_threshold: 实际的分块阈值（已考虑prompt开销）
            prompt_variables: 可选的，用于格式化prompt的额外变量

        Returns:
            GeneralInfoResult: 处理结果
        """
        try:
            # Step 1: 分割文本，使用实际阈值
            text_chunks = self._split_text(text, actual_threshold)

            if len(text_chunks) == 1:
                logger.info("文本分割后只有一个块，回退到单次处理模式")
                return self._process_text_single(text, prompt_variables)

            # Step 2: Map阶段 - 处理各个文本块
            logger.info(f"Map阶段开始，处理 {len(text_chunks)} 个文本块")
            map_results = []

            for i, chunk in enumerate(text_chunks):
                logger.debug(f"处理第 {i+1}/{len(text_chunks)} 个文本块")
                result = self._map_process_text(chunk, prompt_variables)
                map_results.append(result)

            # Step 3: Combine阶段 - 合并结果
            logger.info("开始Combine阶段")
            final_result = self._combine_results(map_results, prompt_variables)

            # Step 4: 后处理
            final_result = self._post_process_result(final_result, input_text=text)

            logger.info(f"Map-Reduce模式处理成功")
            return final_result

        except Exception as e:
            logger.error(f"Map-Reduce模式处理失败: {str(e)}")
            raise

    async def aprocess_text(self, text: str, prompt_variables: Optional[Dict[str, Any]] = None) -> GeneralInfoResult:
        """
        异步处理给定文本。

        Args:
            text: 需要处理的文本
            prompt_variables: 可选的，用于格式化prompt的额外变量

        Returns:
            GeneralInfoResult: 处理结果
        """
        # 在异步环境中运行同步方法
        return await asyncio.to_thread(self.process_text, text, prompt_variables)
