import asyncio
from typing import Dict, List, Any, Optional

from langchain_core.runnables import Runnable, RunnablePassthrough, RunnableConfig
from langchain_core.prompts import BasePromptTemplate
from langchain_core.language_models.base import BaseLanguageModel
from langchain_core.exceptions import OutputParserException
from langchain_core.output_parsers import BaseOutputParser
from langchain_text_splitters import RecursiveCharacterTextSplitter

from tenacity import (
    retry,
    stop_after_attempt,
    wait_exponential,
    retry_if_exception_type,
    RetryCallState
)

from ..prompts.nearby_hotspot_prompt import map_prompt, combine_prompt
from ...core.logger_config import get_logger
from ..schemas.nearby_hotspot_schemas import NearbyHotspotInfoResult, NearbyHotspotInfo
from ..parsers.nearby_hotspot_parser import NearbyHotspotOutputParser
from ..callbacks.nearby_hotspot_callback import NearbyHotspotCallback
from ..configs.nearby_hotspot_config import NearbyHotspotConfig

logger = get_logger()


class NearbyHotspotChain:
    """
    周边热点事件提取链。

    这个类封装了一个完整的周边热点事件提取流水线，包括：
    - LLM 模型调用
    - Prompt 模板应用
    - 输出解析和验证
    - 回调处理
    - 错误处理和重试机制
    - Map-Reduce模式支持（用于处理长文本）

    它使用 LangChain 的 LCEL (LangChain Expression Language) 来构建处理链，
    并提供同步和异步两种调用方式。
    """

    def __init__(
            self,
            llm: BaseLanguageModel,
            prompt: Optional[BasePromptTemplate] = None,
            output_parser: Optional[BaseOutputParser] = None,
            callback_handler: Optional[NearbyHotspotCallback] = None,
            config: Optional[NearbyHotspotConfig] = None
    ):
        """
        初始化周边热点事件提取链。

        Args:
            llm: 用于周边热点事件提取的语言模型实例
            prompt: 可选的 Prompt 模板，如果未提供则使用默认的 map_prompt
            output_parser: 可选的输出解析器，如果未提供则使用默认的 NearbyHotspotOutputParser
            callback_handler: 可选的回调处理器，如果未提供则使用默认的 NearbyHotspotCallback
            config: 可选的配置对象，如果未提供则使用默认的 NearbyHotspotConfig
        """
        if prompt is None:
            raise ValueError("参数 'prompt' (BasePromptTemplate实例) 不能为空。")
        self.prompt: BasePromptTemplate = prompt or map_prompt

        if llm is None:
            raise ValueError("参数 'llm' (BaseLanguageModel实例) 不能为空。")
        self.llm: BaseLanguageModel = llm

        self.output_parser: BaseOutputParser = output_parser or NearbyHotspotOutputParser()
        self.callback_handler: NearbyHotspotCallback = callback_handler or NearbyHotspotCallback()
        self.config: NearbyHotspotConfig = config or NearbyHotspotConfig()

        # 构建核心处理链
        self.chain: Runnable[Dict, NearbyHotspotInfoResult] = self._build_chain()

        logger.info(
            f"NearbyHotspotChain 初始化完成。LLM: {type(self.llm).__name__}, "
            f"Prompt: {type(self.prompt).__name__}, 配置: {self.config}"
        )

    def _build_chain(self) -> Runnable[Dict, NearbyHotspotInfoResult]:
        """构建核心的LCEL处理链。"""
        return (
                RunnablePassthrough()
                | self.prompt
                | self.llm
                | self.output_parser
        )

    def _get_run_config(self) -> RunnableConfig:
        """获取运行时配置，包括回调处理器等。"""
        return RunnableConfig(
            callbacks=[self.callback_handler] if self.callback_handler else [],
            tags=["nearby_hotspot", "llm_processing"],
            metadata={"chain_type": "nearby_hotspot"}
        )

    def _get_tenacity_retry_decorator(self):
        """获取 Tenacity 重试装饰器，用于处理临时性错误。"""

        def _log_retry_attempt(retry_state: RetryCallState) -> None:
            """重试时的日志记录回调。"""
            attempt_number = retry_state.attempt_number
            if retry_state.outcome and retry_state.outcome.failed:
                exception = retry_state.outcome.exception()
                logger.warning(
                    f"Tenacity: 正在重试 NearbyHotspotChain (第 {attempt_number} 次尝试)。"
                    f"下次尝试前等待: {retry_state.next_action.sleep if retry_state.next_action else 0:.2f} 秒 (如果适用)。"
                    f"失败原因: {exception}"
                )
                # 回调处理器没有on_retry方法，这里只记录日志

        return retry(
            stop=stop_after_attempt(self.config.max_retry_attempts),
            wait=wait_exponential(
                multiplier=1,
                min=self.config.retry_wait_min,
                max=self.config.retry_wait_max
            ),
            retry=retry_if_exception_type((OutputParserException, ConnectionError, TimeoutError)),
            after=_log_retry_attempt,
            reraise=True
        )

    def _post_process_result(self, result: NearbyHotspotInfoResult, input_text: str) -> NearbyHotspotInfoResult:
        """
        对提取结果进行后处理，包括验证、过滤和统计更新。

        Args:
            result: 原始的周边热点事件提取结果
            input_text: 输入的原始文本

        Returns:
            NearbyHotspotInfoResult: 经过后处理的结果
        """
        if not result or not result.results:
            logger.warning("周边热点事件提取结果为空或未包含任何周边热点事件数据")
            return NearbyHotspotInfoResult(results=[], content=input_text)

        # 过滤和验证周边热点事件
        valid_hotspots = []
        for hotspot in result.results:
            if self._is_valid_hotspot(hotspot):
                valid_hotspots.append(hotspot)
            else:
                logger.debug(f"过滤掉无效的周边热点事件: {hotspot}")

        # 更新回调统计
        if self.callback_handler:
            self.callback_handler.update_extracted_hotspots_count(len(valid_hotspots))

        # 创建最终结果
        final_result = NearbyHotspotInfoResult(
            results=valid_hotspots,
            content=result.content,
            total=len(valid_hotspots)
        )

        logger.info(f"周边热点事件后处理完成: 原始 {len(result.results)} 个，有效 {len(valid_hotspots)} 个")
        return final_result

    def _is_valid_hotspot(self, hotspot: NearbyHotspotInfo) -> bool:
        """
        验证单个周边热点事件是否有效。

        Args:
            hotspot: 要验证的周边热点事件

        Returns:
            bool: 如果周边热点事件有效则返回 True，否则返回 False
        """
        # 基本字段验证
        if not hotspot.influence_area or not hotspot.influence_area.strip():
            return False
        if not hotspot.event_summary or not hotspot.event_summary.strip():
            return False
        if not hotspot.threat_level or hotspot.threat_level not in ["高", "中", "低"]:
            return False

        # 内容长度验证
        if len(hotspot.event_summary.strip()) < 10:  # 内容太短
            return False

        return True

    def _validate_input(self, text: str) -> None:
        """
        验证输入文本的有效性。

        Args:
            text: 需要验证的输入文本

        Raises:
            ValueError: 如果输入无效
        """
        if not isinstance(text, str):
            raise ValueError("输入必须是字符串类型")

        if not text or not text.strip():
            raise ValueError("输入文本不能为空或仅包含空白字符")

    def _split_text(self, text: str, actual_chunk_size: int) -> List[str]:
        """
        将长文本分割成适合处理的文本块
        
        Args:
            text: 需要分割的文本
            actual_chunk_size: 实际的分块大小（已考虑prompt开销）
            
        Returns:
            List[str]: 分割后的文本块列表
        """
        try:
            # 创建文本分割器，使用实际的分块大小
            text_splitter = RecursiveCharacterTextSplitter(
                chunk_size=actual_chunk_size,
                chunk_overlap=self.config.chunk_overlap,
                length_function=len,
                separators=["\n\n", "\n", "。", "！", "？", "；", " ", ""]
            )
            
            # 分割文本
            chunks = text_splitter.split_text(text)
            logger.info(f"周边热点事件文本分割完成，原文长度: {len(text)}, 实际分块大小: {actual_chunk_size}, 分割成 {len(chunks)} 个文本块")
            
            return chunks
            
        except Exception as e:
            logger.error(f"周边热点事件文本分割失败: {str(e)}")
            # 如果分割失败，返回原文本作为单个块
            return [text]

    def _map_extract_hotspots(self, text_chunk: str, prompt_variables: Optional[Dict[str, Any]] = None) -> str:
        """
        Map阶段：从单个文本块中提取周边热点事件，返回JSON字符串
        
        Args:
            text_chunk: 单个文本块
            prompt_variables: 可选的，用于格式化prompt的额外变量
            
        Returns:
            str: 提取到的周边热点事件JSON字符串
        """
        try:
            logger.debug(f"周边热点事件Map阶段处理文本块，长度: {len(text_chunk)}")
            
            # 构建map阶段的处理链
            map_chain = (
                RunnablePassthrough()
                | map_prompt
                | self.llm
            )
            
            # 准备输入变量
            input_payload = {"text": text_chunk}
            if prompt_variables:
                input_payload.update(prompt_variables)
            
            # 执行map处理
            run_config = self._get_run_config()
            result = map_chain.invoke(input_payload, config=run_config)
            
            # 提取内容
            if hasattr(result, 'content'):
                content = result.content
            else:
                content = str(result)
            
            logger.debug(f"周边热点事件Map阶段完成，返回内容长度: {len(content)}")
            return content
            
        except Exception as e:
            logger.error(f"周边热点事件Map阶段处理失败: {str(e)}")
            # 返回空的JSON数组
            return "[]"

    def _combine_hotspots(self, map_results: List[str], prompt_variables: Optional[Dict[str, Any]] = None) -> NearbyHotspotInfoResult:
        """
        Combine阶段：合并和去重多个map结果

        Args:
            map_results: map阶段返回的JSON字符串列表
            prompt_variables: 可选的，用于格式化prompt的额外变量

        Returns:
            NearbyHotspotInfoResult: 合并后的周边热点事件提取结果
        """
        try:
            logger.debug(f"周边热点事件Combine阶段开始，处理 {len(map_results)} 个map结果")

            # 过滤空结果
            valid_results = [result for result in map_results if result.strip() and result.strip() != "[]"]

            if not valid_results:
                logger.warning("所有周边热点事件map结果都为空，返回空结果")
                return NearbyHotspotInfoResult(results=[], content="")

            # 合并所有map结果
            combined_text = "\n---\n".join(valid_results)

            # 构建combine阶段的处理链
            combine_chain = (
                RunnablePassthrough()
                | combine_prompt
                | self.llm
                | self.output_parser
            )

            # 准备输入变量
            input_payload = {"text": combined_text}
            if prompt_variables:
                input_payload.update(prompt_variables)

            # 执行combine处理，带重试机制
            retry_decorator = self._get_tenacity_retry_decorator()

            @retry_decorator
            def _invoke_combine_with_retry() -> NearbyHotspotInfoResult:
                run_config = self._get_run_config()
                return combine_chain.invoke(input_payload, config=run_config)

            result = _invoke_combine_with_retry()
            logger.info(f"周边热点事件Combine阶段完成，最终提取 {len(result.results)} 个周边热点事件")

            return result

        except Exception as e:
            logger.error(f"周边热点事件Combine阶段处理失败: {str(e)}")
            # 返回空结果
            return NearbyHotspotInfoResult(results=[], content="")

    def _calculate_prompt_overhead(self, actual_text: str, prompt_variables: Optional[Dict[str, Any]] = None) -> int:
        """
        计算prompt的实际长度开销

        Args:
            actual_text: 实际的输入文本
            prompt_variables: 可选的，用于格式化prompt的额外变量

        Returns:
            int: prompt的实际长度开销
        """
        try:
            # 准备输入变量
            input_payload = {"text": actual_text}
            if prompt_variables:
                input_payload.update(prompt_variables)

            # 格式化prompt，获取完整的prompt文本
            formatted_prompt = map_prompt.format(**input_payload)

            # 计算prompt的实际开销 = 完整prompt长度 - 实际文本长度
            prompt_overhead = len(formatted_prompt) - len(actual_text)

            logger.debug(f"周边热点事件计算prompt开销: 完整prompt长度={len(formatted_prompt)}, 实际文本长度={len(actual_text)}, 开销={prompt_overhead}")

            return max(0, prompt_overhead)  # 确保不为负数

        except Exception as e:
            logger.warning(f"周边热点事件计算prompt开销失败: {e}，使用默认值4000")
            return 4000  # 默认开销值

    def extract_hotspots(self, text: str, prompt_variables: Optional[Dict[str, Any]] = None) -> NearbyHotspotInfoResult:
        """
        同步提取给定文本中的周边热点事件，自动选择单次处理或Map-Reduce模式

        Args:
            text: 需要提取周边热点事件的文本
            prompt_variables: 可选的，用于格式化prompt的额外变量

        Returns:
            NearbyHotspotInfoResult: 提取到的周边热点事件结果
        """
        self._validate_input(text)

        # 计算prompt的实际开销
        prompt_overhead = self._calculate_prompt_overhead(text, prompt_variables)

        # 计算实际的文本长度阈值
        actual_threshold = self.config.chunk_size - prompt_overhead

        # 检查阈值是否有效
        if actual_threshold <= 0:
            raise ValueError(
                f"周边热点事件prompt开销({prompt_overhead})超过或等于chunk_size({self.config.chunk_size})，"
                f"无法进行文本处理。请增大chunk_size配置。"
            )

        # 判断是否需要使用Map-Reduce模式
        text_length = len(text)
        use_map_reduce = (
            self.config.enable_map_reduce and
            text_length > actual_threshold
        )

        if use_map_reduce:
            logger.info(f"周边热点事件文本长度 {text_length} 超过实际阈值 {actual_threshold}（chunk_size={self.config.chunk_size}, prompt开销={prompt_overhead}），使用Map-Reduce模式")
            return self._extract_hotspots_map_reduce(text, actual_threshold, prompt_variables)
        else:
            logger.info(f"周边热点事件文本长度 {text_length} 未超过实际阈值 {actual_threshold}，使用单次处理模式")
            return self._extract_hotspots_single(text, prompt_variables)

    def _extract_hotspots_single(self, text: str, prompt_variables: Optional[Dict[str, Any]] = None) -> NearbyHotspotInfoResult:
        """
        单次处理模式：直接处理整个文本

        Args:
            text: 需要提取周边热点事件的文本
            prompt_variables: 可选的，用于格式化prompt的额外变量

        Returns:
            NearbyHotspotInfoResult: 提取到的周边热点事件结果
        """
        retry_decorator = self._get_tenacity_retry_decorator()

        @retry_decorator
        def _invoke_chain_with_retry() -> NearbyHotspotInfoResult:
            logger.debug(f"周边热点事件单次模式处理文本，长度: {len(text)}")
            run_config = self._get_run_config()
            
            # 准备输入变量
            input_payload = {"text": text}
            if prompt_variables:
                input_payload.update(prompt_variables)
                
            raw_result: NearbyHotspotInfoResult = self.chain.invoke(input_payload, config=run_config)
            return self._post_process_result(raw_result, input_text=text)

        try:
            final_result = _invoke_chain_with_retry()
            logger.info(f"周边热点事件单次模式提取成功，共提取 {len(final_result.results)} 个有效周边热点事件")
            return final_result
        except Exception as e:
            logger.error(f"周边热点事件单次模式提取失败: {str(e)}")
            raise

    def _extract_hotspots_map_reduce(self, text: str, actual_threshold: int, prompt_variables: Optional[Dict[str, Any]] = None) -> NearbyHotspotInfoResult:
        """
        Map-Reduce模式：分块处理长文本

        Args:
            text: 需要提取周边热点事件的文本
            actual_threshold: 实际的分块阈值（已考虑prompt开销）
            prompt_variables: 可选的，用于格式化prompt的额外变量

        Returns:
            NearbyHotspotInfoResult: 提取到的周边热点事件结果
        """
        try:
            # Step 1: 分割文本，使用实际阈值
            text_chunks = self._split_text(text, actual_threshold)

            if len(text_chunks) == 1:
                logger.info("周边热点事件文本分割后只有一个块，回退到单次处理模式")
                return self._extract_hotspots_single(text, prompt_variables)

            # Step 2: Map阶段 - 并行处理各个文本块
            logger.info(f"周边热点事件Map阶段开始，处理 {len(text_chunks)} 个文本块")
            map_results = []

            for i, chunk in enumerate(text_chunks):
                logger.debug(f"处理第 {i+1}/{len(text_chunks)} 个周边热点事件文本块")
                result = self._map_extract_hotspots(chunk, prompt_variables)
                map_results.append(result)

            # Step 3: Combine阶段 - 合并和去重结果
            logger.info("开始周边热点事件Combine阶段")
            final_result = self._combine_hotspots(map_results, prompt_variables)

            # Step 4: 后处理
            final_result = self._post_process_result(final_result, input_text=text)

            logger.info(f"周边热点事件Map-Reduce模式提取成功，共提取 {len(final_result.results)} 个有效周边热点事件")
            return final_result

        except Exception as e:
            logger.error(f"周边热点事件Map-Reduce模式提取失败: {str(e)}")
            raise

    async def aextract_hotspots(self, text: str, prompt_variables: Optional[Dict[str, Any]] = None) -> NearbyHotspotInfoResult:
        """
        异步提取给定文本中的周边热点事件。

        Args:
            text: 需要提取周边热点事件的文本
            prompt_variables: 可选的，用于格式化prompt的额外变量

        Returns:
            NearbyHotspotInfoResult: 提取到的周边热点事件结果
        """
        # 在异步环境中运行同步方法
        return await asyncio.to_thread(self.extract_hotspots, text, prompt_variables)
