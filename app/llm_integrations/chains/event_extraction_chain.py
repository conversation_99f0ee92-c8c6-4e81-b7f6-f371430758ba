import json
import asyncio
from typing import Dict, List, Any, Optional

from langchain_core.runnables import Runnable, RunnablePassthrough, RunnableConfig
from langchain_core.prompts import BasePromptTemplate
from langchain_core.language_models.base import BaseLanguageModel
from langchain_core.exceptions import OutputParserException
from langchain_core.output_parsers import BaseOutputParser
from langchain_text_splitters import RecursiveCharacterTextSplitter

from tenacity import (
    retry,
    stop_after_attempt,
    wait_exponential,
    retry_if_exception_type,
    RetryCallState
)

from ..llm_models import LlmModel
from ..prompts.event_extraction_prompt import map_prompt, combine_prompt
from ...core.logger_config import get_logger
from ..schemas.event_extraction_schemas import EventExtractionResult, ExtractedEvent, EventArgument
from ..parsers.event_extraction_parser import EventExtractionOutputParser
from ..callbacks.event_extraction_callback import EventExtractionCallback
from ..configs.event_extraction_config import EventExtractionConfig

logger = get_logger()


class EventExtractionChain:
    """事件提取链"""

    def __init__(
            self,
            llm: BaseLanguageModel,
            prompt: BasePromptTemplate,
            output_parser: Optional[BaseOutputParser[EventExtractionResult]] = None,
            config: Optional[EventExtractionConfig] = None,
    ):
        """
        构造函数。
        Args:
            llm: 必须提供真实的 BaseLanguageModel 实例。
            prompt: 必须提供真实的 BasePromptTemplate 实例。
            output_parser: 可选，自定义输出解析器。
            config: 可选，自定义配置。
        """
        self.config: EventExtractionConfig = config or EventExtractionConfig()

        if llm is None:
            raise ValueError("参数 'llm' (BaseLanguageModel实例) 不能为空。")
        self.llm: BaseLanguageModel = llm or LlmModel().get_openai_compatible_llm()

        if prompt is None:
            raise ValueError("参数 'prompt' (BasePromptTemplate实例) 不能为空。")
        self.prompt: BasePromptTemplate = prompt or map_prompt

        self.output_parser: BaseOutputParser[EventExtractionResult] = output_parser or EventExtractionOutputParser()

        self.callback_handler: Optional[EventExtractionCallback] = None
        if self.config.enable_callbacks:
            self.callback_handler = EventExtractionCallback()

        self.chain: Runnable[Dict, EventExtractionResult] = self._build_chain()

        logger.info(
            f"EventExtractionChain 初始化完成。LLM: {type(self.llm).__name__}, Prompt: {type(self.prompt).__name__}, 配置: {self.config}")

    def _build_chain(self) -> Runnable[Dict, EventExtractionResult]:
        """构建核心的LCEL处理链。"""
        return (
                RunnablePassthrough()
                | self.prompt
                | self.llm
                | self.output_parser
        )

    def _get_tenacity_retry_decorator(self):
        """创建并配置 Tenacity 的重试装饰器

        根据类配置参数构建具备以下特性的重试装饰器：
        - 最大重试次数限制
        - 指数退避等待策略
        - 特定异常类型触发重试
        - 重试前的日志记录和回调触发

        Returns:
            tenacity.retry: 配置完成的重试装饰器实例
        """

        def log_before_sleep_and_trigger_callback(retry_state: RetryCallState):
            """重试等待前的日志记录和回调处理函数

            参数:
                retry_state (RetryCallState): 当前重试状态对象，包含：
                    - 当前重试次数
                    - 下次等待时间
                    - 失败异常对象
            """
            # 获取调用链名称用于日志标识
            chain_display_name = self.callback_handler.chain_name if self.callback_handler else "Chain"

            # 解析重试状态信息
            attempt_num = retry_state.attempt_number
            sleep_duration_attr = getattr(retry_state.next_action, 'sleep', 'N/A')
            # 格式化等待时间为可读字符串
            sleep_duration_str = f"{sleep_duration_attr:.2f}" if isinstance(sleep_duration_attr, float) else str(
                sleep_duration_attr)
            # 获取触发重试的异常对象
            exception_obj = retry_state.outcome.exception() if retry_state.outcome else None

            # 记录包含关键信息的警告日志
            logger.warning(
                f"Tenacity: 正在重试 {chain_display_name} (第 {attempt_num} 次尝试)。"
                f"下次尝试前等待: {sleep_duration_str} 秒 (如果适用)。"
                f"失败原因: {exception_obj if exception_obj else '未知或非异常触发'}"
            )
            # 如果有回调处理器，触发自定义重试回调
            if self.callback_handler:
                self.callback_handler.custom_on_tenacity_retry(retry_state)

        # 构建并返回配置完整的重试装饰器
        return retry(
            # 停止条件：达到最大重试次数
            stop=stop_after_attempt(self.config.max_retry_attempts),
            # 等待策略：指数退避算法，带最小/最大等待限制
            wait=wait_exponential(
                multiplier=1,
                min=self.config.retry_wait_min,
                max=self.config.retry_wait_max
            ),
            # 重试条件：当遇到指定类型异常时触发重试
            retry=retry_if_exception_type((
                OutputParserException,  # 输出解析异常
                json.JSONDecodeError,  # JSON解码错误
                asyncio.TimeoutError,  # 异步超时错误
            )),
            # 重试前执行的回调函数（用于日志记录和事件触发）
            before_sleep=log_before_sleep_and_trigger_callback,
            # 重试耗尽后重新抛出原始异常
            reraise=True
        )

    def _get_run_config(self) -> RunnableConfig:
        """准备并返回传递给 LangChain Runnable 的运行配置对象。

        该方法负责收集当前实例中配置的回调处理器，并将其封装到 RunnableConfig 对象中。
        若未配置回调处理器，则返回的配置对象中 callbacks 字段为 None。

        返回:
            RunnableConfig: 包含回调处理器列表的配置对象，结构如下：
                - callbacks: 经过处理的回调处理器列表（存在时）/None（无回调时）
        """
        # 初始化空回调列表，用于存储待配置的回调处理器
        callbacks_list: List[Any] = []

        # 若实例中配置了回调处理器，则添加到列表中
        if self.callback_handler:
            callbacks_list.append(self.callback_handler)

        # 构造并返回 RunnableConfig 对象，传递处理后的回调列表
        return RunnableConfig(
            callbacks=callbacks_list if callbacks_list else None,
        )

    def _validate_input(self, text: str) -> None:
        """
        验证输入文本是否符合规范要求

        参数:
        text (str): 需要验证的输入文本。要求必须是包含有效内容的非空字符串，
            且长度不超过配置中设定的最大长度限制

        返回:
        None: 通过验证时无返回，若验证失败则抛出 ValueError 异常

        异常:
        ValueError: 当输入不符合以下任一条件时触发:
            1. 输入参数类型不是字符串
            2. 输入文本为空或仅包含空白字符
        """
        # 参数类型校验
        if not isinstance(text, str):
            raise ValueError(f"输入参数 'text' 必须是字符串类型，但得到: {type(text)}")

        # 非空性校验（过滤纯空白字符情况）
        if not text or not text.strip():
            raise ValueError("输入文本 'text' 不能为空字符串或仅包含空白字符。")

    def _split_text(self, text: str, actual_chunk_size: int) -> List[str]:
        """
        将长文本分割成适合处理的文本块

        Args:
            text: 需要分割的文本
            actual_chunk_size: 实际的分块大小（已考虑prompt开销）

        Returns:
            List[str]: 分割后的文本块列表
        """
        try:
            # 创建文本分割器，使用实际的分块大小
            text_splitter = RecursiveCharacterTextSplitter(
                chunk_size=actual_chunk_size,
                chunk_overlap=self.config.chunk_overlap,
                length_function=len,
                separators=["\n\n", "\n", "。", "！", "？", "；", " ", ""]
            )

            # 分割文本
            chunks = text_splitter.split_text(text)
            logger.info(f"文本分割完成，原文长度: {len(text)}, 实际分块大小: {actual_chunk_size}, 分割成 {len(chunks)} 个文本块")

            return chunks

        except Exception as e:
            logger.error(f"文本分割失败: {str(e)}")
            # 如果分割失败，返回原文本作为单个块
            return [text]

    def _map_extract_events(self, text_chunk: str, prompt_variables: Optional[Dict[str, Any]] = None) -> str:
        """
        Map阶段：从单个文本块中提取事件，返回JSON字符串

        Args:
            text_chunk: 单个文本块
            prompt_variables: 可选的，用于格式化prompt的额外变量

        Returns:
            str: 提取到的事件JSON字符串
        """
        try:
            logger.debug(f"Map阶段处理文本块，长度: {len(text_chunk)}")

            # 构建map阶段的处理链
            map_chain = (
                RunnablePassthrough()
                | map_prompt
                | self.llm
            )

            # 准备输入变量
            input_payload = {"text": text_chunk}
            if prompt_variables:
                input_payload.update(prompt_variables)

            # 执行map处理
            run_config = self._get_run_config()
            result = map_chain.invoke(input_payload, config=run_config)

            # 提取内容
            if hasattr(result, 'content'):
                content = result.content
            else:
                content = str(result)

            logger.debug(f"Map阶段完成，返回内容长度: {len(content)}")
            return content

        except Exception as e:
            logger.error(f"Map阶段处理失败: {str(e)}")
            # 返回空的JSON数组
            return "[]"

    def _combine_events(self, map_results: List[str], prompt_variables: Optional[Dict[str, Any]] = None) -> EventExtractionResult:
        """
        Combine阶段：合并和去重多个map结果

        Args:
            map_results: map阶段返回的JSON字符串列表
            prompt_variables: 可选的，用于格式化prompt的额外变量

        Returns:
            EventExtractionResult: 合并后的事件提取结果
        """
        try:
            logger.debug(f"Combine阶段开始，处理 {len(map_results)} 个map结果")

            # 过滤空结果
            valid_results = [result for result in map_results if result.strip() and result.strip() != "[]"]

            if not valid_results:
                logger.warning("所有map结果都为空，返回空结果")
                return EventExtractionResult(results=[], content="")

            # 合并所有map结果
            combined_text = "\n---\n".join(valid_results)

            # 构建combine阶段的处理链
            combine_chain = (
                RunnablePassthrough()
                | combine_prompt
                | self.llm
                | self.output_parser
            )

            # 准备输入变量
            input_payload = {"text": combined_text}
            if prompt_variables:
                input_payload.update(prompt_variables)

            # 执行combine处理，带重试机制
            retry_decorator = self._get_tenacity_retry_decorator()

            @retry_decorator
            def _invoke_combine_with_retry() -> EventExtractionResult:
                run_config = self._get_run_config()
                return combine_chain.invoke(input_payload, config=run_config)

            result = _invoke_combine_with_retry()
            logger.info(f"Combine阶段完成，最终提取 {len(result.results)} 个事件")

            return result

        except Exception as e:
            logger.error(f"Combine阶段处理失败: {str(e)}")
            # 返回空结果
            return EventExtractionResult(results=[], content="")

    def _post_process_result(
            self,
            result: EventExtractionResult,
            input_text: Optional[str] = None
    ) -> EventExtractionResult:
        """
        对事件提取结果进行后处理

        Args:
            result: 待处理的事件提取结果对象。若类型不匹配将返回空结果
            input_text: 可选原始文本输入。当提供时，会覆盖result的content字段

        Returns:
            EventExtractionResult: 处理后的事件提取结果对象。保证至少包含空content字段

        处理流程：
        1. 类型验证：确保输入结果为合法EventExtractionResult类型
        2. 内容处理：优先使用input_text更新结果内容，未提供时校验原始内容是否为空
        3. 回调处理：通过回调接口上报有效事件数量（当启用回调配置时）
        """
        # 参数类型校验层：确保后续处理的对象类型正确
        if not isinstance(result, EventExtractionResult):
            logger.error(f"后处理函数期望得到 EventExtractionResult 类型，但实际收到: {type(result)}。将返回空结果。")
            return EventExtractionResult(results=[], content="")

        # 回调触发层：统计有效事件数量并触发回调
        num_valid_events = len(result.results)
        if self.callback_handler and self.config.enable_callbacks:
            # 支持两种回调处理方式：专用接口或通用接口
            if isinstance(self.callback_handler, EventExtractionCallback):
                self.callback_handler.update_extracted_events_count(num_valid_events)
            elif hasattr(self.callback_handler, 'update_extracted_events_count'):
                getattr(self.callback_handler, 'update_extracted_events_count')(num_valid_events)

        return result

    def _calculate_prompt_overhead(self, actual_text: str, prompt_variables: Optional[Dict[str, Any]] = None) -> int:
        """
        计算prompt的实际长度开销

        Args:
            actual_text: 实际的输入文本
            prompt_variables: 可选的，用于格式化prompt的额外变量

        Returns:
            int: prompt的实际长度开销
        """
        try:
            # 准备输入变量
            input_payload = {"text": actual_text}
            if prompt_variables:
                input_payload.update(prompt_variables)

            # 格式化prompt，获取完整的prompt文本
            formatted_prompt = map_prompt.format(**input_payload)

            # 计算prompt的实际开销 = 完整prompt长度 - 实际文本长度
            prompt_overhead = len(formatted_prompt) - len(actual_text)

            logger.debug(f"计算prompt开销: 完整prompt长度={len(formatted_prompt)}, 实际文本长度={len(actual_text)}, 开销={prompt_overhead}")

            return max(0, prompt_overhead)  # 确保不为负数

        except Exception as e:
            logger.warning(f"计算prompt开销失败: {e}，使用默认值4000")
            return 4000  # 默认开销值

    def extract_events(self, text: str, prompt_variables: Optional[Dict[str, Any]] = None) -> EventExtractionResult:
        """
        同步提取给定文本中的事件，自动选择单次处理或Map-Reduce模式

        Args:
            text: 需要提取事件的文本
            prompt_variables: 可选的，用于格式化prompt的额外变量

        Returns:
            EventExtractionResult: 提取到的事件结果
        """
        self._validate_input(text)

        # 计算prompt的实际开销
        prompt_overhead = self._calculate_prompt_overhead(text, prompt_variables)

        # 计算实际的文本长度阈值
        actual_threshold = self.config.chunk_size - prompt_overhead

        # 检查阈值是否有效
        if actual_threshold <= 0:
            raise ValueError(
                f"prompt开销({prompt_overhead})超过或等于chunk_size({self.config.chunk_size})，"
                f"无法进行文本处理。请增大chunk_size配置。"
            )

        # 判断是否需要使用Map-Reduce模式
        text_length = len(text)
        use_map_reduce = (
            self.config.enable_map_reduce and
            text_length > actual_threshold
        )

        if use_map_reduce:
            logger.info(f"文本长度 {text_length} 超过实际阈值 {actual_threshold}（chunk_size={self.config.chunk_size}, prompt开销={prompt_overhead}），使用Map-Reduce模式")
            return self._extract_events_map_reduce(text, actual_threshold, prompt_variables)
        else:
            logger.info(f"文本长度 {text_length} 未超过实际阈值 {actual_threshold}，使用单次处理模式")
            return self._extract_events_single(text, prompt_variables)

    def _extract_events_single(self, text: str, prompt_variables: Optional[Dict[str, Any]] = None) -> EventExtractionResult:
        """
        单次处理模式：直接处理整个文本

        Args:
            text: 需要提取事件的文本
            prompt_variables: 可选的，用于格式化prompt的额外变量

        Returns:
            EventExtractionResult: 提取到的事件结果
        """
        retry_decorator = self._get_tenacity_retry_decorator()

        @retry_decorator
        def _invoke_chain_with_retry() -> EventExtractionResult:
            logger.debug(f"单次模式处理文本，长度: {len(text)}")
            run_config = self._get_run_config()
            
            # 准备输入变量
            input_payload = {"text": text}
            if prompt_variables:
                input_payload.update(prompt_variables)
                
            raw_result: EventExtractionResult = self.chain.invoke(input_payload, config=run_config)
            return self._post_process_result(raw_result, input_text=text)

        try:
            final_result = _invoke_chain_with_retry()
            logger.info(f"单次模式事件提取成功，共提取 {len(final_result.results)} 个有效事件")
            return final_result
        except Exception as e:
            logger.error(f"单次模式事件提取失败: {str(e)}")
            raise

    def _extract_events_map_reduce(self, text: str, actual_threshold: int, prompt_variables: Optional[Dict[str, Any]] = None) -> EventExtractionResult:
        """
        Map-Reduce模式：分块处理长文本

        Args:
            text: 需要提取事件的文本
            actual_threshold: 实际的分块阈值（已考虑prompt开销）
            prompt_variables: 可选的，用于格式化prompt的额外变量

        Returns:
            EventExtractionResult: 提取到的事件结果
        """
        try:
            # Step 1: 分割文本，使用实际阈值
            text_chunks = self._split_text(text, actual_threshold)

            if len(text_chunks) == 1:
                logger.info("文本分割后只有一个块，回退到单次处理模式")
                return self._extract_events_single(text, prompt_variables)

            # Step 2: Map阶段 - 并行处理各个文本块
            logger.info(f"Map阶段开始，处理 {len(text_chunks)} 个文本块")
            map_results = []

            for i, chunk in enumerate(text_chunks):
                logger.debug(f"处理第 {i+1}/{len(text_chunks)} 个文本块")
                result = self._map_extract_events(chunk, prompt_variables)
                map_results.append(result)

            # Step 3: Combine阶段 - 合并和去重结果
            logger.info("开始Combine阶段")
            final_result = self._combine_events(map_results, prompt_variables)

            # Step 4: 后处理
            final_result = self._post_process_result(final_result, input_text=text)

            logger.info(f"Map-Reduce模式事件提取成功，共提取 {len(final_result.results)} 个有效事件")
            return final_result

        except Exception as e:
            logger.error(f"Map-Reduce模式事件提取失败: {str(e)}")
            raise

    def get_stats(self) -> Optional[Dict[str, Any]]:
        """获取回调处理器的统计信息 (如果回调已启用并实例化)。"""
        if self.callback_handler:
            return self.callback_handler.get_stats()
        else:
            logger.info("回调处理器未启用或未初始化，无法获取统计信息。")
            return None

