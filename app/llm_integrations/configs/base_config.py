from dataclasses import dataclass


@dataclass
class BaseChainConfig:
    """
    所有链配置的基础类。
    定义了通用的配置参数，如文本处理限制、并发设置、重试参数以及回调开关等。
    """
    # 文本处理与请求限制
    max_concurrent_requests: int = 5  # 批量处理时的最大并发请求数
    timeout_seconds: int = 600  # 请求的超时时间 (秒)

    # 重试机制配置 (配合 Tenacity 等库使用)
    max_retry_attempts: int = 3  # 最大重试次数
    retry_wait_min: float = 1.0  # 重试等待的最小时间 (秒)
    retry_wait_max: float = 10.0  # 重试等待的最大时间 (秒)

    # 回调处理器配置
    enable_callbacks: bool = True  # 是否启用回调处理器

    # Map-Reduce 模式配置
    enable_map_reduce: bool = True  # 是否启用Map-Reduce模式
    chunk_size: int = 8000  # 分块大小（包含prompt开销）
    chunk_overlap: int = 200  # 分块重叠大小
    max_combine_attempts: int = 3  # combine阶段的最大重试次数
