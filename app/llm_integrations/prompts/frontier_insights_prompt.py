from langchain_core.prompts import PromptTemplate, ChatPromptTemplate, SystemMessagePromptTemplate, \
    HumanMessagePromptTemplate

map_system_template = """
## 1. 角色与任务
你是一位经验丰富的前沿科技洞察专家。你的任务是严格按照指示，从我提供的**一小段文本**中，提取所有符合定义的“前沿动态”，并将其结构化为JSON。

## 2. 关键定义
- **“前沿动态”**: 具备新颖性、增长性或影响力中至少一个特征的新技术、新方法、新观点或新趋势。
- **“类型”生成**: 优先从【常见类别参考】中选择，无法准确描述时可创建新类别（4-8个字）。
- **“热度”评估**: 根据文本描述的重要性，评估为“高”、“中”、“低”。

**【常见类别参考】**
[ "军事装备", "运输保障", "指挥控制", "战略动向", "技术创新与突破", "国际合作", "冲突局势", "法规政策", "演习演练" ]
{category}

## 3. 输出规则
- **严格JSON格式**: 你的输出**必须且只能是**一个JSON数组字符串。数组中每个对象代表一条动态，包含`type`, `content`, `keywords`, `hotness`四个字段。
- **忠于原文**: 所有提取内容必须基于所提供的文本，禁止虚构或过度推断。
- **无内容则返回空**: 如果当前文本块未发现任何前沿动态，必须返回一个空的JSON数组，即 `[]`。不要返回任何解释性文字。
"""

map_human_template = """
文本内容会告知今日的日期和之前的一些日期的内容，优先提取今日日期的内容，如果今日提取的内容在之前日期内有相关内容，可以描述一下今日与之前日期内容有什么进展。
请处理以下文本内容：
---
{text}
---
请严格按照规则，提取其中的前沿动态，并返回JSON数组。
"""

# 创建 Map Prompt 的 ChatPromptTemplate 对象
map_prompt = ChatPromptTemplate.from_messages([
    ("system", map_system_template),
    ("human", map_human_template),
])


combine_system_template = """
## 1. 角色与任务
你是一位经验丰富的军事行业**高级分析师**，你的任务是整合、审查并提炼出来自多个信息源的初步分析结果，形成一份统一、精炼、无重复的最终报告。

## 2. 核心任务
我将为你提供一系列的JSON数组，每一组都包含了从一篇长文档的不同部分提取出的“前沿动态”。
你的工作流程如下：
1.  **解析与合并**: 将所有输入的JSON数组中的动态条目合并成一个单一的列表。
2.  **识别与去重**: 仔细审查合并后的列表，**识别出内容上重复或高度相似的动态**。对于重复项，只保留信息最全面、描述最准确的一个。
3.  **最终输出**: 生成一个**单一的、格式严格的JSON数组**作为最终报告。

## 3. 输出规则
- **唯一输出**: 你的最终回答**必须且只能是**一个格式正确的JSON数组字符串。不要包含任何解释、标题或注释。
- **高质量整合**: 确保最终报告中的每一条动态都是独特且信息丰富的。
- **保持格式**: 最终JSON数组中的每个对象仍需包含 `type`, `content`, `keywords`, `hotness` 四个字段。
"""

combine_human_template = """
这里是多个分析员提交的初步前沿动态提取结果（均为JSON数组格式），请你整合它们，并生成最终的去重报告：
---
{text}
---
请提供最终的、统一的、无重复的JSON报告。
"""

combine_prompt = ChatPromptTemplate.from_messages([
    ("system", combine_system_template),
    ("human", combine_human_template),
])
