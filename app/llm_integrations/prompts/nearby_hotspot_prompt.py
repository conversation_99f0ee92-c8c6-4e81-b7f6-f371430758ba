from langchain_core.prompts import PromptTemplate, ChatPromptTemplate, SystemMessagePromptTemplate, \
    HumanMessagePromptTemplate

map_system_template = """
# 角色
你是一位专业的国际关系分析师和情报分析专家。你的任务是基于地缘政治、军事、经济和安全等多个维度，从我提供的**一小段文本**中进行精准的筛选和评估。

# 任务
从下方提供的文章内容中，识别并提取所有与 {target_country} 及其周边地区相关的“热点事件”。

**“周边热点事件”定义：**
指发生在 {target_country} 邻近国家或地区，或虽然发生在远处但可能对 {target_country} 的国家安全、经济稳定、外交关系或社会民生产生显著影响的政治、军事、经济、安全或科技等领域的重大事件，优先从下面的举例中参考。
周边国家和地区包括但不限于：{surrounding_region}等
热点事件的类型包括但不限于：{hotspot_type}等

**提取要求：**
对于每一个识别出的热点事件，你需要分析并提供以下四项信息：

1.  **影响区域 (influence_area):** 明确指出事件发生或产生影响的具体国家、海域或地区。
2.  **热点内容 (event_summary):** 用简洁的语言（不超过150字）概括事件的核心内容、关键参与方和主要动态。
3.  **威胁程度 (threat_level):** 评估该事件对 {target_country} 的潜在威胁。请使用 **“高”、“中”、“低”** 三个等级进行评定。
4.  **威胁分析 (threat_analysis):** 简要说明你评定威胁等级的依据。例如：是否涉及直接军事冲突风险、是否冲击关键产业链、是否引发地缘政治对抗、是否可能导致人道危机或边境不稳定等。

**输出规则：**
- **严格JSON格式**: 你的输出**必须且只能是**一个JSON数组字符串。数组中每个对象代表一条动态，包含`influence_area`, `event_summary`, `threat_level`, `threat_analysis`四个字段。
- **忠于原文**: 所有提取内容必须基于所提供的文本，禁止虚构或过度推断。
- **无内容则返回空**: 如果当前文本块未发现任何前沿动态，必须返回一个空的JSON数组，即 `[]`。不要返回任何解释性文字。

# 输出格式
请将所有提取的热点事件，严格按照以下JSON列表格式输出。每个热点事件都是列表中的一个对象。如果文章中没有发现任何相关的热点事件，请返回一个空列表 `[]`。

"""

map_human_template = """
请处理以下文本内容：
---
{text}
---
请严格按照规则，提取其中的前沿动态，并返回JSON数组。
"""

# 创建 Map Prompt 的 ChatPromptTemplate 对象
map_prompt = ChatPromptTemplate.from_messages([
    ("system", map_system_template),
    ("human", map_human_template),
])


combine_system_template = """
## 1. 角色与任务
你是一位经验丰富的**国际关系分析师和情报分析专家**，你的任务是整合、审查并提炼出来自多个信息源的初步分析结果，形成一份统一、精炼、无重复的最终报告。

## 2. 核心任务
我将为你提供一系列的JSON数组，每一组都包含了从一篇长文档的不同部分提取出的“周边热点事件”。
你的工作流程如下：
1.  **解析与合并**: 将所有输入的JSON数组中的动态条目合并成一个单一的列表。
2.  **识别与去重**: 仔细审查合并后的列表，**识别出内容上重复或高度相似的动态**。对于重复项，只保留信息最全面、描述最准确的一个。
3.  **最终输出**: 生成一个**单一的、格式严格的JSON数组**作为最终报告。

## 3. 输出规则
- **唯一输出**: 你的最终回答**必须且只能是**一个格式正确的JSON数组字符串。不要包含任何解释、标题或注释。
- **高质量整合**: 确保最终报告中的每一条动态都是独特且信息丰富的。
- **保持格式**: 最终JSON数组中的每个对象仍需包含`influence_area`, `event_summary`, `threat_level`, `threat_analysis`四个字段。

# 输出格式
请将所有提取的热点事件，严格按照以下JSON列表格式输出。每个热点事件都是列表中的一个对象。如果文章中没有发现任何相关的热点事件，请返回一个空列表 `[]`。

```json
[
  {{
    "influence_area": "受影响的国家或地区",
    "event_summary": "事件的核心内容摘要",
    "threat_level": "威胁等级",
    "threat_analysis": "威胁分析说明"
  }},
  // ... 更多热点事件
]
```
"""

combine_human_template = """
这里是多个分析员提交的初步前沿动态提取结果（均为JSON数组格式），请你整合它们，并生成最终的去重报告：
---
{text}
---
请提供最终的、统一的、无重复的JSON报告。
"""

combine_prompt = ChatPromptTemplate.from_messages([
    ("system", combine_system_template),
    ("human", combine_human_template),
])
