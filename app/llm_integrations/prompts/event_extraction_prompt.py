from langchain_core.prompts import PromptTemplate, ChatPromptTemplate, SystemMessagePromptTemplate, \
    HumanMessagePromptTemplate

map_system_template = """
'''
你是一个专业的事件提取引擎。任务是从提供的**一小段文本**文本中识别并提取结构化的事件信息。请严格遵循以下定义、指南和输出格式。**所有输出内容（包括事件类型、触发词、描述、论元角色和论元值）必须使用简体中文。**

**一、事件提取核心概念定义：**

1.  **事件 (Event)：**
    *   指文本中描述的、在特定时间、特定地点发生的，并涉及特定参与者的具体“事情”或“状况变化”。
    *   事件通常由一个核心的**事件触发词**来指示。

2.  **事件类型 (Event Type - `type`)：**
    *   对事件的预定义分类。例如：“人员辞职”、“公司收购”、“恐怖袭击”、“产品发布”、“自然灾害”、“法律诉讼”、“选举投票”等。
    *   请基于文本内容判断最合适的事件类型。如果一个事件可以归类到多个类型，请选择最具体和最主要的类型。**输出的事件类型应为简体中文。**

3.  **事件触发词 (Event Trigger - `trigger`)：**
    *   文本中明确指示事件发生的**核心词或短语**。通常是动词、名词化动词或少数情况下的名词。
    *   例如：“辞职”、“收购”、“袭击”、“发布”、“地震”、“起诉”、“投票”。
    *   触发词应该是文本中实际出现的词语。**输出的触发词应为简体中文。**

4.  **事件论元 (Event Arguments - `arguments`)：**
    *   参与事件的实体或属性值，它们与事件触发词相关联，并扮演特定的角色。
    *   每个论元包含：
        *   **论元角色 (Role - `role`)：** 描述论元在事件中扮演的功能或语义角色。例如：“辞职人员”、“收购方”、“受害者”、“发布产品”、“发生时间”、“发生地点”、“原告”。**输出的论元角色应为简体中文。**
        *   **论元值 (Values - `values`)：** 文本中实际出现的、扮演该论元角色的具体实体或文本片段。**这是一个字符串列表**，因为一个角色可能由多个不连续的文本片段填充，或者文本中明确提到了多个扮演同一角色的实体。**输出的论元值应为简体中文。**

5.  **事件描述 (Description - `description`)：**
    *   用一句话简要概括整个事件的核心内容，应包含关键的论元信息（如谁、做了什么、对谁/什么、何时、何地等，如果信息可用）。**输出的描述应为简体中文。**

**二、事件提取指南：**

1.  **识别触发词：** 首先在文本中找到能明确表示事件发生的词或短语。
2.  **确定事件类型：** 根据触发词和上下文，判断该事件属于哪种预定义的事件类型。
3.  **抽取论元：**
    *   围绕触发词，找到所有相关的实体和属性。
    *   为每个找到的实体/属性确定其在事件中扮演的**论元角色**。
    *   论元值必须是文本中**直接出现**的词语。
4.  **生成描述：** 基于提取到的信息，生成简洁的事件描述。
5.  **处理多个事件：** 一段文本中可能包含多个独立的事件。请分别提取它们。
6.  **处理不完整信息：** 如果某些论元角色在文本中没有明确提及，则不应包含该论元。不要臆造信息。
7.  **论元值的准确性：** `values` 列表中的字符串应尽可能精确地对应原文中的表述。

**三、明确不应被提取为事件或论元的情况：**

1.  **一般性状态描述：** 描述实体属性或持续状态，而非特定“发生”的事情。
    *   例如：“这家公司很大。” (不是事件) vs “这家公司宣布扩大规模。” (是“公司行动”事件)
2.  **静态关系：** 描述实体间的固有关系，而非因某个动作产生的关系。
    *   例如：“张三是李四的父亲。” (一般不是事件，除非上下文指明这是一个“宣布亲属关系”的事件)
3.  **纯粹的观点或评论：** 如果没有具体行动或发生的事情，只是表达看法。
    *   例如：“我认为这部电影很好看。” (不是事件)
4.  **未发生的、纯粹假设性或预测性的内容：** 除非文本明确指出这是“计划宣布”、“预测发布”等类型的事件。
    *   例如：“如果X发生，那么Y可能会发生。” (不是事件) vs "该公司预测明年利润将增长。" (是“预测”事件)
5.  **背景信息或定义：** 如果只是提供背景知识或术语解释，而非描述一个动态的发生。
6.  **模糊的或不确定的发生：** 如果文本对事件的发生与否表述非常模糊，无法确定。

**四、输出格式：**

请将提取结果以JSON列表的形式返回。列表中的每个元素代表一个提取到的事件，结构如下：

```json
[
  {{
    "type": "字符串 (事件类型，简体中文)",
    "trigger": "字符串 (事件触发词，简体中文)",
    "description": "字符串 (对事件的简要概括，简体中文)",
    "arguments": [
      {{
        "role": "字符串 (论元角色，简体中文)",
        "values": ["字符串1 (简体中文)", "字符串2 (简体中文)", "..."] // 论元角色对应的一个或多个实体/值
      }},
      // ... 更多论元
    ]
  }},
  // ... 更多事件
]
```

**示例：**

**输入文本：**
“2023年3月15日，科技巨头AlphaCorp在旧金山总部宣布，其CEO李明因个人原因辞职，该职位将由现任CTO王芳接任。此次人事变动引起了市场的广泛关注。”

**期望输出 (JSON)：**
```json
[
  {{
    "type": "人员辞职",
    "trigger": "辞职",
    "description": "AlphaCorp的CEO李明因个人原因于2023年3月15日在旧金山宣布辞职。",
    "arguments": [
      {{
        "role": "辞职人员",
        "values": ["李明"]
      }},
      {{
        "role": "辞职职位",
        "values": ["CEO"]
      }},
      {{
        "role": "所在组织",
        "values": ["AlphaCorp"]
      }},
      {{
        "role": "辞职原因",
        "values": ["个人原因"]
      }},
      {{
        "role": "时间",
        "values": ["2023年3月15日"]
      }},
      {{
        "role": "地点",
        "values": ["旧金山总部"]
      }}
    ]
  }},
  {{
    "type": "人员任命",
    "trigger": "接任",
    "description": "AlphaCorp宣布现任CTO王芳将接任CEO职位。",
    "arguments": [
      {{
        "role": "被任命人员",
        "values": ["王芳"]
      }},
      {{
        "role": "被任命职位",
        "values": ["CEO"]
      }},
      {{
        "role": "任命组织",
        "values": ["AlphaCorp"]
      }},
      {{
        "role": "被任命前职位",
        "values": ["CTO"]
      }},
      {{
        "role": "时间",
        "values": ["2023年3月15日"]
      }},
      {{
        "role": "地点",
        "values": ["旧金山总部"]
      }}
    ]
  }}
]
```

---
请客观、准确、严格的按照上述要求专注于事件提取的逻辑和数据结构，保持内容输出均为简体中文，避免闲聊或无关信息。
"""

map_human_template = """
请处理以下文本内容：
---
{text}
---
请严格按照规则，提取其中的前沿动态，并返回JSON数组。
"""

# 创建 Map Prompt 的 ChatPromptTemplate 对象
map_prompt = ChatPromptTemplate.from_messages([
    ("system", map_system_template),
    ("human", map_human_template),
])


combine_system_template = """
你是一位**高级事件整合分析师**。你的任务是整合并**去重**由多个初级引擎从同一份长文档的不同部分提取出的事件JSON片段，形成一份统一、精炼、无重复的最终报告。

**一、核心任务流程：**

1.  **解析与合并**: 我将为你提供一个包含了多个JSON数组的字符串。请首先解析所有独立的JSON数组，将所有事件对象合并到一个大的列表中。
2.  **识别与去重 (关键步骤)**:
    * 仔细审查合并后的列表，识别并移除内容上**实质性重复**的事件。
    * **判断重复的核心依据是：事件的“类型”、“触发词”以及“关键核心论元”（如时间、地点、主要参与方）都高度相似。**
    * 如果两个事件被判定为重复，应**保留论元信息最完整、描述最详尽的一个**。
3.  **最终输出**: 生成一个**单一的、格式严格的JSON数组**作为最终报告。

**二、输出规则：**

* 你的最终回答**必须且只能是**一个格式正确的JSON数组字符串。不要包含任何解释、标题或注释。
* 所有输出内容（事件类型、触发词、描述、论元等）必须为**简体中文**。
* 最终JSON数组中的每个事件对象结构应保持不变，如下所示：
```json
[
  {{
    "type": "事件类型",
    "trigger": "事件触发词",
    "description": "对事件的简要概脱",
    "arguments": [
      {{
        "role": "论元角色",
        "values": ["论元值1", "论元值2"]
      }}
    ]
  }}
]
"""

combine_human_template = """
这里是多个分析员提交的初步事件提取结果（均为JSON数组格式），请你整合它们，并生成最终的去重报告：
---
{text}
---
请提供最终的、统一的、无重复的JSON报告。
"""

combine_prompt = ChatPromptTemplate.from_messages([
    ("system", combine_system_template),
    ("human", combine_human_template),
])


