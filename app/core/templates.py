from pathlib import Path
from fastapi.templating import Jinja2Templates


class TemplateService:
    def __init__(self):
        self._templates = None

    def init_templates(self, base_dir: Path):
        self._templates = Jinja2Templates(directory=str(base_dir))

    @property
    def templates(self):
        if self._templates is None:
            raise RuntimeError("模板引擎未初始化！")
        return self._templates


# 单例模式全局访问点
template_service = TemplateService()
