import os

from pydantic import AnyHttpUrl, field_validator, Field
from pydantic_settings import BaseSettings, SettingsConfigDict
from typing import Optional, List, Union


class Settings(BaseSettings):
    ENCODING: str = 'utf-8'
    # 项目根目录路径
    PROJECT_ROOT: str = os.path.abspath(os.path.join(os.path.dirname(__file__), '..', '..'))
    APP_ROOT: str = os.path.join(PROJECT_ROOT, 'app')
    TEMPLATES_ROOT: str = os.path.join(APP_ROOT, 'templates')
    RESOURCES_ROOT: str = os.path.join(PROJECT_ROOT, 'resources')

    # 项目基本配置
    PROJECT_NAME: str = "LLMFlowHub"
    PROJECT_VERSION: str = "1.0.0"
    PROJECT_DESCRIPTION: str = "LLM 文本处理 API"

    # FastAPI 服务器配置
    HOST: str = "0.0.0.0"  # 服务器监听地址，0.0.0.0表示监听所有地址
    PORT: int = 8000  # 服务器端口号
    MAX_CONTENT_LENGTH: int = 100000  # API可接收的最大内容长度
    RELOAD: bool = False  # 是否开启自动重载，开发环境建议设为True
    DEBUG: bool = False  # 调试模式开关
    ENVIRONMENT: str = "production"  # 环境：development, production
    TIMEZONE: str = "Asia/Shanghai"  # 时区设置
    URL_PREFIX: str = ""  # 路由前缀

    # 日志配置
    LOG_LEVEL: str = "INFO"  # 日志级别：DEBUG, INFO, WARNING, ERROR, CRITICAL
    LOG_FORMAT: str = "[%(asctime)s] [%(levelname)s] [%(name)s] [%(module)s:%(lineno)d] %(message)s"  # 日志格式
    LOG_FILE_PATH: Optional[str] = None  # 日志文件路径，不设置则只输出到控制台

    # LLM 配置
    LLM_API_BASE: AnyHttpUrl = "http://localhost:11434/v1"
    LLM_API_KEY: str = "sk-knowind"
    LLM_MODEL_NAME: str = "qwen3:0.6b"
    THINK_MODE: bool = False

    # MySQL 配置
    # 目前可能暂时不需要MySQL了
    MYSQL_HOST: str = "localhost"  # MySQL 服务器地址
    MYSQL_PORT: int = 3306  # MySQL 端口号
    MYSQL_DB: str = "llmflowhub_db"  # MySQL 数据库名称
    MYSQL_USER: str = "root"  # MySQL 用户名
    MYSQL_PASSWORD: str = "mysql_password"  # MySQL 密码

    # Redis 配置
    REDIS_HOST: str = "localhost"  # Redis 服务器地址
    REDIS_PORT: int = 6379  # Redis 端口号
    REDIS_DB: int = 0  # Redis 存储队列数据的数据库索引号
    REDIS_RESULT_DB: int = 1  # Redis 存储任务结果的数据库索引号
    REDIS_PASSWORD: Optional[str] = None  # Redis 密码，可选

    # Celery 任务配置
    MAX_RETRIES: int = 3  # 任务最大重试次数
    RETRY_DELAY: int = 30  # 重试延迟时间，单位秒
    TASK_TIMEOUT: int = 3600  # 任务超时时间，单位秒
    MAX_CONCURRENT_TASKS: int = 1  # 最大并发任务数
    RESULT_CACHE_TIME: int = 604800  # 结果缓存时间，单位秒

    # 回调配置
    CALLBACK_TIMEOUT: int = 60  # 回调请求超时时间，单位秒
    CALLBACK_RETRIES: int = 3  # 回调最大重试次数
    CALLBACK_RETRY_DELAY: int = 10  # 回调重试延迟时间，单位秒

    # 派生配置（由其他配置项计算得出）
    DATABASE_URL: Optional[str] = None  # 数据库连接URL，例如：mysql+pymysql://root:password@localhost:3306/llmflowhub_db
    CELERY_BROKER_URL: Optional[str] = None  # Celery Broker URL，例如：redis://:password@localhost:6379/0
    CELERY_RESULT_BACKEND: Optional[str] = None  # Celery 结果后端URL，例如：redis://:password@localhost:6379/1

    @field_validator("DATABASE_URL", mode="before")
    @classmethod
    def build_database_url(cls, v: Optional[str], info) -> str:
        """构建 MySQL 数据库连接 URL"""
        if isinstance(v, str):
            return v
        values = info.data
        # 示例：mysql+pymysql://root:password@localhost:3306/llmflowhub_db
        return f"mysql+pymysql://{values.get('MYSQL_USER')}:{values.get('MYSQL_PASSWORD')}@{values.get('MYSQL_HOST')}:{values.get('MYSQL_PORT')}/{values.get('MYSQL_DB')}"

    @field_validator("CELERY_BROKER_URL", mode="before")
    @classmethod
    def build_celery_broker_url(cls, v: Optional[str], info) -> str:
        """构建 Celery Broker URL（Redis）"""
        if isinstance(v, str):
            return v
        values = info.data
        password_part = f":{values.get('REDIS_PASSWORD')}@" if values.get('REDIS_PASSWORD') else ""
        # 示例：redis://:password@localhost:6379/0
        return f"redis://{password_part}{values.get('REDIS_HOST')}:{values.get('REDIS_PORT')}/{values.get('REDIS_DB')}"

    @field_validator("CELERY_RESULT_BACKEND", mode="before")
    @classmethod
    def build_celery_result_backend(cls, v: Optional[str], info) -> Optional[str]:
        """构建 Celery 结果后端 URL（Redis），如果需要的话，否则保持为 None"""
        if isinstance(v, str):
            return v
        values = info.data
        password_part = f":{values.get('REDIS_PASSWORD')}@" if values.get('REDIS_PASSWORD') else ""
        # 示例：redis://:password@localhost:6379/1
        return f"redis://{password_part}{values.get('REDIS_HOST')}:{values.get('REDIS_PORT')}/{values.get('REDIS_RESULT_DB')}"

    model_config = SettingsConfigDict(
        env_file=".env",
        env_file_encoding='utf-8',
        case_sensitive=False,
        extra='ignore'
    )


settings = Settings()
