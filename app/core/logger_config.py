import logging
import sys
import os
import inspect
from logging.handlers import TimedRotatingFileHandler
from datetime import datetime
import click
from app.core.config import settings


class ColorizedFormatter(logging.Formatter):
    """
    彩色日志格式化器，为不同级别的日志提供不同颜色。

    颜色映射：
    - DEBUG: 青色
    - INFO: 绿色
    - WARNING: 黄色
    - ERROR: 红色
    - CRITICAL: 亮红色
    """
    level_name_colors = {
        logging.DEBUG: lambda level_name: click.style(str(level_name), fg="cyan"),
        logging.INFO: lambda level_name: click.style(str(level_name), fg="green"),
        logging.WARNING: lambda level_name: click.style(str(level_name), fg="yellow"),
        logging.ERROR: lambda level_name: click.style(str(level_name), fg="red"),
        logging.CRITICAL: lambda level_name: click.style(str(level_name), fg="bright_red"),
    }

    def __init__(self, fmt=None, datefmt=None, style='%', use_colors=None):
        super().__init__(fmt, datefmt, style)
        self.use_colors = use_colors if use_colors is not None else sys.stdout.isatty()

    def format(self, record):
        levelname = ''
        if self.use_colors:
            levelname = record.levelname
            if record.levelno in self.level_name_colors:
                record.levelname = self.level_name_colors[record.levelno](levelname)
        result = super().format(record)
        record.levelname = levelname
        return result


# 用于跟踪已配置的logger，避免重复配置
_configured_loggers = set()


def get_logger(name=None):
    """
    获取配置好的日志记录器，支持自动识别调用模块。

    Args:
        name (str, optional): 日志记录器名称。如果为 None，则自动使用调用者的模块名。

    Returns:
        logging.Logger: 配置好的日志记录器。

    Example:
        ```python
        # 方式1：手动指定名称
        logger = get_logger("user_service")

        # 方式2：自动识别模块名（推荐）
        logger = get_logger()  # 会自动使用当前模块名，如 app.services.user_service

        # 方式3：获取根logger
        logger = get_logger("")  # 空字符串获取根logger
        ```
    """
    if name is None:
        # 自动获取调用者的模块名
        frame = inspect.currentframe().f_back
        module_name = frame.f_globals.get('__name__', 'unknown')

        # 简化模块名，去掉 app. 前缀，使日志更简洁
        if module_name.startswith('app.'):
            module_name = module_name[4:]  # 去掉 'app.' 前缀
        elif module_name == '__main__':
            # 如果是主模块，尝试获取文件名
            filename = frame.f_globals.get('__file__', 'main')
            if filename:
                module_name = os.path.splitext(os.path.basename(filename))[0]
            else:
                module_name = 'main'

        name = module_name

    return logging.getLogger(name)


def setup_logging(service_name):
    """
    根据配置设置应用程序的日志系统。

    Args:
        service_name (str): 服务名称，用于区分不同服务，同时作为日志文件名。

    功能特点：
    1. 服务隔离：不同服务有独立的日志配置和文件
    2. 集中管理：统一的日志配置接口
    3. 彩色输出：终端中使用彩色输出，提高可读性
    4. 文件日志：每个服务独立的日志文件，按日期滚动
    5. 防止重复：避免多次调用导致的日志处理器重复问题

    使用方法：
    ```python
    # 在主服务入口
    from app.core.logger_config import setup_logging, get_logger
    setup_logging("main_service")
    logger = get_logger()
    logger.info("主服务启动")

    # 在队列服务入口
    setup_logging("queue_service")
    logger = get_logger()
    logger.info("队列服务启动")

    # 在业务模块中
    logger = get_logger("user_handler")
    logger.info("用户处理模块")
    ```
    """
    # 从配置中获取日志级别
    log_level = settings.LOG_LEVEL.upper()

    # 获取根日志记录器
    logger = logging.getLogger()

    # 防止重复配置
    if service_name in _configured_loggers:
        return logger

    logger.setLevel(log_level)

    # 清理现有处理器（如果有的话）
    if logger.hasHandlers():
        logger.handlers.clear()

    # 创建控制台处理器
    console_handler = logging.StreamHandler(sys.stdout)
    console_handler.setLevel(log_level)

    # 创建彩色格式化器，包含服务名称
    console_format = f"[{service_name}] %(levelname)s: %(asctime)s - %(name)s - %(message)s"
    console_formatter = ColorizedFormatter(console_format)
    console_handler.setFormatter(console_formatter)

    # 将控制台处理器添加到日志记录器
    logger.addHandler(console_handler)

    # 文件日志配置 - 使用服务名称作为文件名
    logs_dir = os.path.join(settings.PROJECT_ROOT, 'logs')
    if not os.path.exists(logs_dir):
        os.makedirs(logs_dir)

    log_filename = f"{service_name}.log"
    file_handler = TimedRotatingFileHandler(
        os.path.join(logs_dir, log_filename),
        encoding="utf-8",
        when="midnight",
        interval=1,
        backupCount=30
    )
    file_handler.setLevel(log_level)

    # 文件格式包含更多信息，便于调试
    file_format = f"%(asctime)s - [{service_name}] - %(levelname)s - %(name)s - %(funcName)s:%(lineno)d - %(message)s"
    file_formatter = logging.Formatter(file_format)
    file_handler.setFormatter(file_formatter)
    logger.addHandler(file_handler)

    # 标记该服务已配置
    _configured_loggers.add(service_name)

    # 记录配置完成信息
    logger.info(f"日志系统已配置 - 服务: {service_name}, 级别: {log_level}, 文件: {log_filename}")

    return logger
