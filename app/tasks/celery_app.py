from celery import Celery
from celery.signals import setup_logging as celery_setup_logging
from app.core.config import settings
from app.core.logger_config import setup_logging, get_logger

# 获取日志记录器
logger = get_logger()


# 使用 Celery 信号拦截 Celery 的日志设置，使用我们自己的日志配置
@celery_setup_logging.connect
def configure_celery_logging(*args, **kwargs):
    """
    配置 Celery 使用与主应用相同的日志配置。
    
    这个函数会在 Celery 初始化日志系统时被调用，我们拦截这个过程，
    使用我们自己的日志配置，确保 Celery 与主应用使用相同的日志格式和级别。
    
    注意：Celery 和主应用使用相同的 LOGGER_NAME，但它们运行在不同的进程中，
    因此不会互相抢占日志。每个进程都有自己的日志处理器实例。
    """
    # 设置 Celery 使用我们的日志配置
    setup_logging("worker")
    return True


celery_app = Celery(
    settings.PROJECT_NAME + "_worker",
    broker=settings.CELERY_BROKER_URL,
    backend=settings.CELERY_RESULT_BACKEND  # 默认情况下 backend 为 None
)

celery_app.conf.update(
    task_serializer='json',
    accept_content=['json'],
    result_serializer='json',
    timezone=settings.TIMEZONE,
    enable_utc=True,
    imports=[
        'app.tasks.llm_tasks',
        'app.tasks.callback_tasks',
    ],
    # 推荐的配置设置，以提高任务处理的鲁棒性
    task_acks_late=True,  # 在任务执行完成后确认任务（ack）
    worker_prefetch_multiplier=1,  # 每个 worker 预取一个任务，保证任务公平分发
    worker_concurrency=settings.MAX_CONCURRENT_TASKS,  # 每个 worker 的最大并发任务数
    broker_connection_retry_on_startup=True,  # 启动时重试连接消息代理
    task_time_limit=settings.TASK_TIMEOUT if settings.TASK_TIMEOUT else None,
    # 设置任务执行超时时间（单位：秒）
    task_reject_on_worker_lost=True,  # 如果 worker 进程丢失，则重新入队任务
    task_track_started=True,  # 跟踪任务开始执行的时间点

    # 当消息代理不可用时，发布任务的重试策略
    task_publish_retry=True,
    task_publish_retry_policy={
        'max_retries': settings.MAX_RETRIES,  # 最大重试次数
        'interval_start': settings.RETRY_DELAY,  # 初始重试延迟（秒）
        'interval_step': 3,  # 每次重试延迟增加的步长
        'interval_max': settings.RETRY_DELAY if settings.RETRY_DELAY else 5.0,
        # 重试间隔的最大值（秒）
    },

    # 结果后端配置（Redis）
    result_expires=settings.RESULT_CACHE_TIME if settings.RESULT_CACHE_TIME else 604800,  # 结果缓存过期时间（秒）
    result_backend_connection_timeout=10.0,  # 连接结果后端的超时时间
    result_backend_max_retries=3,  # 结果后端连接失败时的最大重试次数
)
