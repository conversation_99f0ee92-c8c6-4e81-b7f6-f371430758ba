from datetime import datetime
from typing import Dict, Any, Optional
from app.tasks.celery_app import celery_app
from app.services.event_extraction import extract_events
from app.services.frontier_insights import extract_insights
from app.services.nearby_hotspot import extract_hotspots
from app.services.general import process_text as process_general_text
from app.services.schemas.callback_schemas import CallbackSuccessData, CallbackFailureData, CallbackTaskResult
from app.api.schemas.task_schemas import TaskType, TaskStatus
from app.core.logger_config import get_logger

logger = get_logger()


@celery_app.task(bind=True, name="tasks.event_extraction")
def event_extraction_task(self, content: str, callback_url: str = None, echo: str = None, prompt_variables: Optional[Dict[str, Any]] = None) -> Dict[str, Any]:
    """
    事件提取异步任务。

    Args:
        self: Celery 任务实例 (通过 bind=True 注入)
        content: 需要提取事件的文本内容
        callback_url: 可选的回调URL
        echo: 可选的回显参数
        prompt_variables: 可选的，用于格式化prompt的额外变量

    Returns:
        Dict[str, Any]: 包含事件提取结果的字典
    """
    try:
        task_id = self.request.id
        logger.info(f"开始执行事件提取任务 {task_id}，文本长度: {len(content)}")

        # 更新任务状态为STARTED
        self.update_state(
            state='STARTED',
            meta={
                'task_id': task_id,
                'task_type': TaskType.EVENT_EXTRACTION,
                'started_at': datetime.now().isoformat(),
                'progress': 0,
                'echo': echo
            }
        )

        # 调用事件提取服务
        start_time = datetime.now()
        events_result = extract_events(content, prompt_variables=prompt_variables)
        end_time = datetime.now()
        processing_time = (end_time - start_time).total_seconds()

        # 构建任务结果数据
        task_result_data = CallbackTaskResult(
            content=events_result.content,
            results=[event.model_dump() for event in events_result.results],
            time=events_result.time.strftime('%Y-%m-%d %H:%M:%S'),
            total=events_result.total
        )

        # 构建返回结果（用于Celery结果存储）
        result = {
            'task_id': task_id,
            'task_type': TaskType.EVENT_EXTRACTION,
            'status': TaskStatus.SUCCESS,
            'result': task_result_data.model_dump(),
            'submitted_at': self.request.eta.isoformat() if self.request.eta else start_time.isoformat(),
            'started_at': start_time.isoformat(),
            'completed_at': end_time.isoformat(),
            'processing_time': processing_time,
            'echo': echo
        }

        logger.info(f"事件提取任务 {task_id} 成功完成，提取了 {events_result.total} 个事件，耗时 {processing_time:.2f}秒")

        # 如果有回调URL，触发回调任务
        if callback_url:
            # 构建标准化的回调数据
            callback_data = CallbackSuccessData(
                task_id=task_id,
                task_type=TaskType.EVENT_EXTRACTION,
                status=TaskStatus.SUCCESS,
                result=task_result_data,
                submitted_at=self.request.eta.isoformat() if self.request.eta else start_time.isoformat(),
                started_at=start_time.isoformat(),
                completed_at=end_time.isoformat(),
                processing_time=processing_time,
                echo=echo
            )

            from app.tasks.callback_tasks import send_task_callback
            send_task_callback.delay(callback_url, callback_data.model_dump())

        return result

    except Exception as e:
        task_id = self.request.id
        error_message = str(e)
        logger.error(f"事件提取任务 {task_id} 执行失败: {error_message}")

        # 更新任务状态为FAILURE
        self.update_state(
            state=TaskStatus.FAILURE,
            meta={
                'task_id': task_id,
                'task_type': TaskType.EVENT_EXTRACTION,
                'error_message': error_message,
                'failed_at': datetime.now().isoformat(),
                'echo': echo
            }
        )

        # 如果有回调URL，发送失败通知
        if callback_url:
            # 构建标准化的失败回调数据
            failure_callback_data = CallbackFailureData(
                task_id=task_id,
                task_type=TaskType.EVENT_EXTRACTION,
                status=TaskStatus.FAILURE,
                error_message=error_message,
                failed_at=datetime.now().isoformat(),
                submitted_at=self.request.eta.isoformat() if self.request.eta else None,
                started_at=None,  # 失败时可能还没开始
                echo=echo
            )

            from app.tasks.callback_tasks import send_task_callback
            send_task_callback.delay(callback_url, failure_callback_data.model_dump())

        # 抛出一个异常，避免序列化问题
        raise Exception(error_message)


@celery_app.task(bind=True, name="tasks.process_text_with_llm")
def process_text_with_llm(self, text_input: str) -> dict:
    """
    使用 LLM 处理文本的示例任务。

    Args:
        self: Celery 任务实例 (通过 bind=True 注入)
        text_input: 需要处理的文本字符串

    Returns:
        dict: 包含处理结果的字典
    """
    try:
        # 模拟LLM处理
        result = {"processed_text": f"LLM processed: {text_input}", "original_text": text_input}
        return result
    except Exception as e:
        logger.error(f"文本处理任务失败: {str(e)}")
        raise


@celery_app.task(bind=True, name="tasks.frontier_insights")
def frontier_insights_task(self, content: str, callback_url: str = None, echo: str = None, prompt_variables: Optional[Dict[str, Any]] = None) -> Dict[str, Any]:
    """
    前沿动态提取异步任务。

    Args:
        self: Celery 任务实例 (通过 bind=True 注入)
        content: 需要提取前沿动态的文本内容
        callback_url: 可选的回调URL
        echo: 可选的回显参数
        prompt_variables: 可选的，用于格式化prompt的额外变量

    Returns:
        Dict[str, Any]: 包含前沿动态提取结果的字典
    """
    try:
        task_id = self.request.id
        logger.info(f"开始执行前沿动态提取任务 {task_id}，文本长度: {len(content)}")

        # 更新任务状态为STARTED
        self.update_state(
            state=TaskStatus.STARTED,
            meta={
                'task_id': task_id,
                'task_type': TaskType.FRONTIER_INSIGHTS,
                'started_at': datetime.now().isoformat(),
                'progress': 0,
                'echo': echo
            }
        )

        # 调用前沿动态提取服务
        start_time = datetime.now()
        insights_result = extract_insights(content, prompt_variables=prompt_variables)
        end_time = datetime.now()
        processing_time = (end_time - start_time).total_seconds()

        # 构建任务结果数据
        task_result_data = CallbackTaskResult(
            content=insights_result.content,
            results=[insight.model_dump() for insight in insights_result.results],
            time=insights_result.time.isoformat(),
            total=insights_result.total
        )

        # 构建返回结果
        result = {
            'task_id': task_id,
            'task_type': TaskType.FRONTIER_INSIGHTS,
            'status': TaskStatus.SUCCESS,
            'result': task_result_data.model_dump(),
            'submitted_at': self.request.eta.isoformat() if self.request.eta else start_time.isoformat(),
            'started_at': start_time.isoformat(),
            'completed_at': end_time.isoformat(),
            'processing_time': processing_time,
            'echo': echo
        }

        logger.info(f"前沿动态提取任务 {task_id} 成功完成，提取了 {insights_result.total} 个前沿动态，耗时 {processing_time:.2f}秒")

        # 如果有回调URL，触发回调任务
        if callback_url:
            # 构建标准化的回调数据
            callback_data = CallbackSuccessData(
                task_id=task_id,
                task_type=TaskType.FRONTIER_INSIGHTS,
                status=TaskStatus.SUCCESS,
                result=task_result_data,
                submitted_at=self.request.eta.isoformat() if self.request.eta else start_time.isoformat(),
                started_at=start_time.isoformat(),
                completed_at=end_time.isoformat(),
                processing_time=processing_time,
                echo=echo
            )

            from app.tasks.callback_tasks import send_task_callback
            send_task_callback.delay(callback_url, callback_data.model_dump())

        return result

    except Exception as e:
        task_id = self.request.id
        error_message = str(e)
        logger.error(f"前沿动态提取任务 {task_id} 执行失败: {error_message}")

        # 更新任务状态为FAILURE
        self.update_state(
            state=TaskStatus.FAILURE,
            meta={
                'task_id': task_id,
                'task_type': TaskType.FRONTIER_INSIGHTS,
                'error': error_message,
                'failed_at': datetime.now().isoformat(),
                'echo': echo
            }
        )

        # 如果有回调URL，发送失败回调
        if callback_url:
            failure_callback_data = CallbackFailureData(
                task_id=task_id,
                task_type=TaskType.FRONTIER_INSIGHTS,
                status=TaskStatus.FAILURE,
                error_message=error_message,
                failed_at=datetime.now().isoformat(),
                echo=echo
            )

            from app.tasks.callback_tasks import send_task_callback
            send_task_callback.delay(callback_url, failure_callback_data.model_dump())

        # 抛出一个简单的异常，避免序列化问题
        raise Exception(error_message)


@celery_app.task(bind=True, name="tasks.nearby_hotspot")
def nearby_hotspot_task(self, content: str, callback_url: str = None, echo: str = None, prompt_variables: Optional[Dict[str, Any]] = None) -> Dict[str, Any]:
    """
    周边热点事件提取异步任务。

    Args:
        self: Celery 任务实例 (通过 bind=True 注入)
        content: 需要提取周边热点事件的文本内容
        callback_url: 可选的回调URL
        echo: 可选的回显参数
        prompt_variables: 可选的，用于格式化prompt的额外变量

    Returns:
        Dict[str, Any]: 包含周边热点事件提取结果的字典
    """
    try:
        task_id = self.request.id
        logger.info(f"开始执行周边热点事件提取任务 {task_id}，文本长度: {len(content)}")

        # 更新任务状态为STARTED
        self.update_state(
            state=TaskStatus.STARTED,
            meta={
                'task_id': task_id,
                'task_type': TaskType.NEARBY_HOTSPOT,
                'started_at': datetime.now().isoformat(),
                'progress': 0,
                'echo': echo
            }
        )

        # 调用周边热点事件提取服务
        start_time = datetime.now()
        hotspots_result = extract_hotspots(content, prompt_variables=prompt_variables)
        end_time = datetime.now()
        processing_time = (end_time - start_time).total_seconds()

        # 构建任务结果数据
        task_result_data = CallbackTaskResult(
            content=hotspots_result.content,
            results=[hotspot.model_dump() for hotspot in hotspots_result.results],
            time=hotspots_result.time.isoformat(),
            total=hotspots_result.total
        )

        # 构建返回结果
        result = {
            'task_id': task_id,
            'task_type': TaskType.NEARBY_HOTSPOT,
            'status': TaskStatus.SUCCESS,
            'result': task_result_data.model_dump(),
            'submitted_at': self.request.eta.isoformat() if self.request.eta else start_time.isoformat(),
            'started_at': start_time.isoformat(),
            'completed_at': end_time.isoformat(),
            'processing_time': processing_time,
            'echo': echo
        }

        logger.info(f"周边热点事件提取任务 {task_id} 成功完成，提取了 {hotspots_result.total} 个周边热点事件，耗时 {processing_time:.2f}秒")

        # 如果有回调URL，触发回调任务
        if callback_url:
            # 构建标准化的回调数据
            callback_data = CallbackSuccessData(
                task_id=task_id,
                task_type=TaskType.NEARBY_HOTSPOT,
                status=TaskStatus.SUCCESS,
                result=task_result_data,
                submitted_at=self.request.eta.isoformat() if self.request.eta else start_time.isoformat(),
                started_at=start_time.isoformat(),
                completed_at=end_time.isoformat(),
                processing_time=processing_time,
                echo=echo
            )

            from app.tasks.callback_tasks import send_task_callback
            send_task_callback.delay(callback_url, callback_data.model_dump())

        return result

    except Exception as e:
        task_id = self.request.id
        error_message = str(e)
        logger.error(f"周边热点事件提取任务 {task_id} 执行失败: {error_message}")

        # 更新任务状态为FAILURE
        self.update_state(
            state=TaskStatus.FAILURE,
            meta={
                'task_id': task_id,
                'task_type': TaskType.NEARBY_HOTSPOT,
                'error': error_message,
                'failed_at': datetime.now().isoformat(),
                'echo': echo
            }
        )

        # 如果有回调URL，发送失败回调
        if callback_url:
            failure_callback_data = CallbackFailureData(
                task_id=task_id,
                task_type=TaskType.NEARBY_HOTSPOT,
                status=TaskStatus.FAILURE,
                error_message=error_message,
                failed_at=datetime.now().isoformat(),
                echo=echo
            )

            from app.tasks.callback_tasks import send_task_callback
            send_task_callback.delay(callback_url, failure_callback_data.model_dump())

        # 抛出一个简单的异常，避免序列化问题
        raise Exception(error_message)


@celery_app.task(bind=True, name="tasks.general_llm")
def general_llm_task(self, content: str, callback_url: str = None, echo: str = None, prompt_variables: Optional[Dict[str, Any]] = None) -> Dict[str, Any]:
    """
    通用LLM调用异步任务。

    Args:
        self: Celery 任务实例 (通过 bind=True 注入)
        content: 需要处理的文本内容
        callback_url: 可选的回调URL
        echo: 可选的回显参数
        prompt_variables: 可选的，用于格式化prompt的额外变量

    Returns:
        Dict[str, Any]: 包含处理结果的字典
    """
    try:
        task_id = self.request.id
        logger.info(f"开始执行通用LLM调用任务 {task_id}，文本长度: {len(content)}")

        # 更新任务状态为STARTED
        self.update_state(
            state=TaskStatus.STARTED,
            meta={
                'task_id': task_id,
                'task_type': TaskType.GENERAL_LLM,
                'started_at': datetime.now().isoformat(),
                'progress': 0,
                'echo': echo
            }
        )

        # 调用通用LLM处理服务
        start_time = datetime.now()
        general_result = process_general_text(content, prompt_variables=prompt_variables)
        end_time = datetime.now()
        processing_time = (end_time - start_time).total_seconds()

        # 构建任务结果数据
        task_result_data = CallbackTaskResult(
            content=general_result.content,
            results=[],  # 通用LLM调用没有特定的结构化结果列表
            time=general_result.time.strftime('%Y-%m-%d %H:%M:%S'),
            total=1  # 通用处理总是返回1个结果
        )

        # 构建返回结果（用于Celery结果存储）
        result = {
            'task_id': task_id,
            'task_type': TaskType.GENERAL_LLM,
            'status': TaskStatus.SUCCESS,
            'result': task_result_data.model_dump(),
            'submitted_at': self.request.eta.isoformat() if self.request.eta else start_time.isoformat(),
            'started_at': start_time.isoformat(),
            'completed_at': end_time.isoformat(),
            'processing_time': processing_time,
            'echo': echo
        }

        logger.info(f"通用LLM调用任务 {task_id} 成功完成，结果长度: {len(general_result.content)}，耗时 {processing_time:.2f}秒")

        # 如果有回调URL，触发回调任务
        if callback_url:
            # 构建标准化的回调数据
            callback_data = CallbackSuccessData(
                task_id=task_id,
                task_type=TaskType.GENERAL_LLM,
                status=TaskStatus.SUCCESS,
                result=task_result_data,
                submitted_at=self.request.eta.isoformat() if self.request.eta else start_time.isoformat(),
                started_at=start_time.isoformat(),
                completed_at=end_time.isoformat(),
                processing_time=processing_time,
                echo=echo
            )

            from app.tasks.callback_tasks import send_task_callback
            send_task_callback.delay(callback_url, callback_data.model_dump())

        return result

    except Exception as e:
        task_id = self.request.id
        error_message = str(e)
        logger.error(f"通用LLM调用任务 {task_id} 执行失败: {error_message}")

        # 更新任务状态为FAILURE
        self.update_state(
            state=TaskStatus.FAILURE,
            meta={
                'task_id': task_id,
                'task_type': TaskType.GENERAL_LLM,
                'error_message': error_message,
                'failed_at': datetime.now().isoformat(),
                'echo': echo
            }
        )

        # 如果有回调URL，发送失败通知
        if callback_url:
            # 构建标准化的失败回调数据
            failure_callback_data = CallbackFailureData(
                task_id=task_id,
                task_type=TaskType.GENERAL_LLM,
                status=TaskStatus.FAILURE,
                error_message=error_message,
                failed_at=datetime.now().isoformat(),
                submitted_at=self.request.eta.isoformat() if self.request.eta else None,
                echo=echo
            )

            from app.tasks.callback_tasks import send_task_callback
            send_task_callback.delay(callback_url, failure_callback_data.model_dump())

        # 抛出一个简单的异常，避免序列化问题
        raise Exception(error_message)
