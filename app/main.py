import time
import logging
from contextlib import asynccontextmanager
from fastapi import FastAP<PERSON>, Request, HTTPException
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import JSONResponse

from app.api.router import api_router
from app.core.config import settings
from app.core.logger_config import setup_logging, get_logger

# 配置日志
setup_logging("app")
logger = get_logger()


@asynccontextmanager
async def lifespan(app: FastAPI):
    """应用程序生命周期管理器，处理启动和关闭事件"""
    logger.info("应用程序启动中...")
    logger.info(f"项目名称: {settings.PROJECT_NAME}")
    logger.info(f"日志级别: {settings.LOG_LEVEL.upper()}")

    # 在日志中屏蔽敏感信息
    # db_url_display = str(settings.DATABASE_URL)
    # if settings.MYSQL_PASSWORD:
    #     db_url_display = db_url_display.replace(settings.MYSQL_PASSWORD, "********")
    # logger.info(f"数据库 URL: {db_url_display}")

    celery_broker_url_display = str(settings.CELERY_BROKER_URL)
    if settings.REDIS_PASSWORD:
        celery_broker_url_display = celery_broker_url_display.replace(settings.REDIS_PASSWORD, "********")
    logger.info(f"Celery Broker URL: {celery_broker_url_display}")

    # async with engine.begin() as conn:
    #     # 在生产环境中，使用 Alembic 进行数据库迁移
    #     # await conn.run_sync(Base.metadata.drop_all) # 仅开发环境：删除所有表
    #     await conn.run_sync(Base.metadata.create_all)  # 如果表不存在则创建
    # logger.info("数据库表已创建/验证。")

    # TODO: 在此添加任何其他启动逻辑（例如，连接到外部服务，预加载模型）
    logger.info(f"{settings.PROJECT_NAME} 应用程序启动完成。")
    yield

    logger.info("应用程序关闭中...")
    # await engine.dispose()
    logger.info("数据库连接池已释放。")
    # TODO: 在此添加任何其他关闭逻辑（例如，关闭连接）
    logger.info(f"{settings.PROJECT_NAME} 应用程序关闭完成。")


app = FastAPI(
    title=settings.PROJECT_NAME,
    version=settings.PROJECT_VERSION,
    description=settings.PROJECT_DESCRIPTION,
    openapi_url=f"{settings.URL_PREFIX}/openapi.json",
    root_path="",
    lifespan=lifespan,
    debug=settings.DEBUG  # 从设置控制调试模式
)

if settings.ENVIRONMENT == "development":
    # 跨域配置，仅限开发时使用
    app.add_middleware(
        CORSMiddleware,
        allow_origins=["*"],
        allow_credentials=True,
        allow_methods=["*"],
        allow_headers=["*"],
    )
    logger.info("应用启动，开发模式，跨域允许所有来源")


# 请求处理时间中间件
# @app.middleware("http")
# async def add_process_time_header(request: Request, call_next):
#     """添加请求处理时间头部的中间件，用于性能监控"""
#     start_time = time.perf_counter()  # 比 time.time() 更精确
#     response = await call_next(request)
#     process_time = time.perf_counter() - start_time
#     response.headers["X-Process-Time"] = f"{process_time:.4f}"
#     logger.debug(f"请求 {request.method} {request.url.path} 处理耗时 {process_time:.4f}秒")
#     return response

@app.middleware("http")
async def log_request_response(request: Request, call_next):
    """
    记录请求和响应的日志中间件。

    参数:
    - request (Request): 当前HTTP请求对象。
    - call_next: 一个可调用对象，用于将请求传递给下一个中间件或最终的视图函数。

    返回:
    - 对于包含大写字母的路径，返回一个RedirectResponse对象，重定向到小写路径。
    - 否则，返回下一个中间件或视图函数的响应结果。
    """
    url_path = request.url.path
    logger.info(f"接收请求: {request.method} {url_path}")
    response = await call_next(request)
    logger.info(f"响应结果: {response.status_code} {url_path}")
    return response


# 全局异常处理器
@app.exception_handler(HTTPException)
async def http_exception_handler(request: Request, exc: HTTPException):
    """HTTP 异常处理器，处理所有 FastAPI 的 HTTP 异常"""
    logger.error(f"HTTP异常: 状态码={exc.status_code}, 详情='{exc.detail}', 路径='{request.url.path}'")
    return JSONResponse(
        status_code=exc.status_code,
        content={"detail": exc.detail if exc.detail else "发生HTTP错误"}  # 提供默认详情
    )


@app.exception_handler(Exception)
async def general_exception_handler(request: Request, exc: Exception):
    """通用异常处理器，捕获所有未处理的异常"""
    logger.error(f"未处理异常: 类型='{type(exc).__name__}', 消息='{exc}', 路径='{request.url.path}'", exc_info=True)
    return JSONResponse(
        status_code=500,
        content={"detail": "服务器内部错误。发生了意外错误。"}
    )


app.include_router(api_router)

logger.info(f"{settings.PROJECT_NAME} (v{settings.PROJECT_VERSION}) 应用程序已初始化。")
