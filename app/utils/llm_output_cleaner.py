import re


class LLMOutputCleaner:
    """
    一个用于清理和规范化大型语言模型（LLM）文本输出的类。
    它可以移除常见的、不需要的格式化内容。
    """

    def __init__(self,
                 remove_tags: list[str] = None,
                 strip_markdown: bool = False,
                 normalize_whitespace: bool = True,
                 custom_regex_remove: list[str] = None):
        """
        使用指定的清理选项初始化清理器。

        参数:
            remove_tags (list[str], optional): 一个XML式标签名称列表（不带尖括号），
                                               其内容（包括标签本身）将被移除。
                                               默认为 ["think"]。
                                               例如: ["think", "scratchpad"]
            strip_markdown (bool, optional): 如果为 True，则尝试移除基本的Markdown格式
                                             （粗体、斜体、行内代码、代码块），保留其内容。
                                             默认为 False。
            normalize_whitespace (bool, optional): 如果为 True，则去除首尾空白、
                                                   将多个连续空格缩减为一个，以及
                                                   将多个连续换行符最多缩减为两个（即一个空行）。
                                                   默认为 True。
            custom_regex_remove (list[str], optional): 一个自定义正则表达式模式的列表。
                                                       任何匹配这些模式的内容都将被移除。
                                                       默认为 None。
        """
        if remove_tags is None:
            # 如果用户没有提供 remove_tags，则默认移除 "think" 标签
            self.remove_tags = ["think"]
        else:
            self.remove_tags = remove_tags

        self.strip_markdown = strip_markdown
        self.normalize_whitespace = normalize_whitespace
        self.custom_regex_remove = custom_regex_remove if custom_regex_remove else []

    def _remove_xml_like_block(self, text: str, tag_name: str) -> str:
        """移除由 <tag_name>...</tag_name> 包裹的文本块。"""
        # 为了稳健性，使标签名匹配不区分大小写
        # 正则表达式解释:
        # <{tag_name}\b[^>]*> : 匹配开始标签，如 <tag_name> 或 <tag_name attr="value">
        #   - <{tag_name}\b : 匹配 "<" 加上标签名，\b 确保是完整的单词（例如，think 不会匹配 thinking）
        #   - [^>]*         : 匹配开始标签中可能的属性，[^>]* 匹配任何非 ">" 的字符零次或多次
        #   - >             : 匹配开始标签的结束 ">"
        # .*?                 : 匹配标签内的任何内容（包括换行符，因为有 re.DOTALL）。
        #                     `?`使其成为非贪婪匹配，即匹配尽可能少的字符。
        # </{tag_name}>       : 匹配结束标签 </tag_name>
        pattern = re.compile(rf"<{tag_name}\b[^>]*>.*?</{tag_name}>", flags=re.DOTALL | re.IGNORECASE)
        # re.DOTALL (或 re.S) 使 '.' 可以匹配包括换行符在内的任何字符。
        # re.IGNORECASE (或 re.I) 使匹配不区分大小写。
        return pattern.sub("", text)  # 用空字符串替换匹配到的内容

    def _strip_basic_markdown_formatting(self, text: str) -> str:
        """
        移除基本的Markdown格式，尝试保留原始内容。
        注意：这是一个简化的Markdown剥离器，可能无法覆盖所有情况或复杂的嵌套。
        """
        # 移除代码块 (```lang...\ncode\n``` 或 ```\ncode\n```)
        # 这个版本移除代码块的包围符号，保留内部代码内容
        text = re.sub(r"```[\w\s]*\n(.*?)\n```", r"\1", text, flags=re.DOTALL)
        # 移除行内代码 (`code`)，保留内容
        text = re.sub(r"`([^`]+)`", r"\1", text)
        # 移除粗体/斜体 (**text** 或 __text__)，保留内容
        # (?=\S) 和 (?<=\S) 确保星号/下划线紧邻非空白字符，避免错误匹配例如 a * list * item
        text = re.sub(r"(\*\*|__)(?=\S)(.+?)(?<=\S)\1", r"\2", text)
        # 移除斜体/粗体 (*text* 或 _text_)，保留内容
        text = re.sub(r"(\*|_)(?=\S)(.+?)(?<=\S)\1", r"\2", text)
        # 移除删除线 (~~text~~)，保留内容
        text = re.sub(r"~~(?=\S)(.+?)(?<=\S)~~", r"\1", text)
        # 移除Markdown标题 (例如, # 标题, ## 标题)，保留标题文本
        # ^\s* 表示行首允许有空白，#{1,6} 匹配1到6个#，\s+ 匹配标题标记和文本间的空白，(.+) 捕获标题文本，$ 表示行尾
        # flags=re.MULTILINE (或 re.M) 使 ^ 和 $ 匹配每一行的开始和结束，而不仅仅是整个字符串的开始和结束。
        text = re.sub(r"^\s*#{1,6}\s+(.+)$", r"\1", text, flags=re.MULTILINE)
        # 移除基本的Markdown链接格式 [显示文本](链接地址) -> 显示文本
        text = re.sub(r"\[([^\]]+)\]\([^\)]+\)", r"\1", text)
        # 移除Markdown图片格式 ![alt text](image url) -> alt text (如果需要完全移除，替换为"")
        # text = re.sub(r"!\[([^\]]*)\]\([^\)]+\)", r"\1", text) # 保留 alt text
        text = re.sub(r"!\[[^\]]*\]\([^\)]+\)", "", text)  # 完全移除图片标记
        # 移除Markdown列表标记 (*, -, +, 1.)，保留列表项内容，这比较复杂，简化处理
        text = re.sub(r"^\s*[\*\-\+]\s+", "", text, flags=re.MULTILINE)  # 移除无序列表项标记
        text = re.sub(r"^\s*\d+\.\s+", "", text, flags=re.MULTILINE)  # 移除有序列表项标记
        return text

    def _normalize_whitespace_rules(self, text: str) -> str:
        """应用空白字符规范化规则。"""
        # 将多个连续空格替换为单个空格
        text = re.sub(r" +", " ", text)
        # 将多个连续的制表符替换为单个空格（或者可以移除，根据需求）
        text = re.sub(r"\t+", " ", text)
        # 将3个或更多连续换行符替换为2个换行符（即保留最多一个空行）
        text = re.sub(r"\n{3,}", "\n\n", text)
        # 去除整个字符串首尾的空白字符（包括空格、制表符、换行符等）
        text = text.strip()
        return text

    def clean(self, text: str) -> str:
        """
        对输入文本应用所有已配置的清理操作。

        参数:
            text (str): 来自LLM的原始文本输出。

        返回:
            str: 清理后的文本。
        """
        if not isinstance(text, str):
            # 如果输入不是字符串，尝试转换为字符串，或可以引发 TypeError
            # raise TypeError("输入必须是字符串")
            return str(text)

        cleaned_text = text

        # 步骤 1: 移除指定的XML式标签及其内容
        for tag in self.remove_tags:
            cleaned_text = self._remove_xml_like_block(cleaned_text, tag)

        # 步骤 2: 移除自定义正则表达式匹配的内容
        for pattern_str in self.custom_regex_remove:
            try:
                # 如果一个模式会被多次使用，预编译 (re.compile) 可以提高效率，
                # 但对于单次 re.sub 调用，直接使用字符串模式也是可以的。
                # 默认添加 re.DOTALL 以便自定义模式能匹配多行内容。
                cleaned_text = re.sub(pattern_str, "", cleaned_text, flags=re.DOTALL)
            except re.error as e:
                # 如果自定义的正则表达式无效，打印警告信息
                print(f"警告: 无效的自定义正则表达式模式 '{pattern_str}': {e}")
                # 此处可以选择更正式的日志记录或重新抛出异常

        # 步骤 3: 如果启用了，剥离Markdown格式
        if self.strip_markdown:
            cleaned_text = self._strip_basic_markdown_formatting(cleaned_text)

        # 步骤 4: 如果启用了，规范化空白字符（通常最后执行效果较好）
        if self.normalize_whitespace:
            cleaned_text = self._normalize_whitespace_rules(cleaned_text)

        return cleaned_text


# --- 示例用法 ---
if __name__ == "__main__":
    # 默认清理器 (移除 <think> 块并规范化空白)
    default_cleaner = LLMOutputCleaner()

    # 同时剥离Markdown并移除 <scratchpad> 标签的清理器
    markdown_stripping_cleaner = LLMOutputCleaner(
        remove_tags=["think", "scratchpad"],  # 要移除的标签列表
        strip_markdown=True,  # 启用Markdown剥离
        normalize_whitespace=True  # 启用空白规范化
    )

    # 带有自定义正则表达式移除规则的清理器
    custom_regex_cleaner = LLMOutputCleaner(
        custom_regex_remove=[
            r"CONFIDENTIAL:.*?END_CONFIDENTIAL",  # 示例：移除保密信息块
            r"DEBUG_INFO:.*"  # 示例：移除单行调试信息 (直到行尾)
        ]
    )

    sample_text_1 = """
    这是主要信息。
    <think>
    用户想知道关于 X 的事情。
    我应该检查资源 A 和 B。
    好的，方案准备好了。
    </think>
    所以，根据我的分析，X 就是 Y。
    <scratchpad>
    这是一些内部的草稿笔记。
    不应该被用户看到。
    </scratchpad>
    另外，请注意 **加粗的重要内容** 和 *斜体的强调*。
    这里有一些代码：
    ```python
    print("你好，世界")
    ```
    以及最后的想法。
    还有图片 ![替代文本](url.jpg) 和链接 [访问官网](http://example.com)
    * 列表项1
    1. 有序列表项
    """

    sample_text_2 = """
    这段文本有    太多的空格。


    以及过多的   换行。

    <think>我应该移除这些吗？</think>
    是的，可能应该。
    CONFIDENTIAL:
    秘密密钥: 12345
    算法: XYZ
    END_CONFIDENTIAL
    常规文本继续。
    DEBUG_INFO: 系统负载 50%
    """

    sample_text_3 = "# 主标题\n## 副标题\n这是一个 `行内代码` 以及一个链接 [OpenAI](https://openai.com)。"

    print("--- 默认清理器处理示例 1 ---")
    cleaned1_default = default_cleaner.clean(sample_text_1)
    print(f"原始文本:\n'''{sample_text_1}'''")
    print(f"清理后:\n'''{cleaned1_default}'''\n")

    print("--- Markdown剥离清理器处理示例 1 ---")
    cleaned1_markdown = markdown_stripping_cleaner.clean(sample_text_1)
    print(f"原始文本:\n'''{sample_text_1}'''")
    print(f"清理后:\n'''{cleaned1_markdown}'''\n")

    print("--- 自定义正则清理器处理示例 2 ---")
    cleaned2_custom = custom_regex_cleaner.clean(sample_text_2)
    print(f"原始文本:\n'''{sample_text_2}'''")
    print(f"清理后:\n'''{cleaned2_custom}'''\n")

    print("--- Markdown剥离清理器处理示例 3 (Markdown特定内容) ---")
    cleaned3_markdown = markdown_stripping_cleaner.clean(sample_text_3)
    print(f"原始文本:\n'''{sample_text_3}'''")
    print(f"清理后:\n'''{cleaned3_markdown}'''\n")

    # 示例：不进行空白规范化
    no_whitespace_norm_cleaner = LLMOutputCleaner(normalize_whitespace=False)
    text_with_spaces = "  <think>abc</think>  首尾有空格  "
    cleaned_no_norm = no_whitespace_norm_cleaner.clean(text_with_spaces)
    print("--- 不进行空白规范化 ---")
    print(f"原始文本:\n'''{text_with_spaces}'''")
    # 注意："首尾有空格" 部分周围的空格会保留，因为 normalize_whitespace=False
    print(f"清理后 (无空白规范化):\n'''{cleaned_no_norm}'''")
