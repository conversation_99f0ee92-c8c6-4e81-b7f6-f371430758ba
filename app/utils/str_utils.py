import re
from bs4 import BeautifulSoup
import unicodedata


def find_matching_strings(article, string_array, case_sensitive=False):
    """
    在文章中查找与字符串数组中的字符串匹配的所有字符串。

    参数:
    article (str): 要搜索的文章内容。
    string_array (list of str): 待查找的字符串数组。
    case_sensitive (bool, optional): 是否区分大小写。默认为False，即不区分大小写。

    返回:
    list of str: 所有在文章中找到的字符串数组中的字符串。
    """
    # 预处理文章和字符串数组
    article = clean_text(article)
    processed_article = article if case_sensitive else article.lower()
    results = []

    # 使用集合快速去重，同时去除空字符串
    unique_strings = {s for s in string_array if s.strip()}

    # 遍历去重后的字符串集合
    for s in unique_strings:
        # 根据是否区分大小写来处理字符串
        processed_str = s if case_sensitive else s.lower()
        # 如果处理后的字符串在处理后的文章中出现，则将其添加到结果列表中
        if processed_str in processed_article:
            results.append(s)

    # 返回结果列表
    return results


def clean_text(text,
               remove_html=True,
               remove_line_breaks=True,
               remove_control_chars=True,
               normalize_whitespace=True,
               remove_special_symbols=True,
               full_to_half=False,
               remove_emoji=True,
               keep_common_punct=True):
    """
    多功能文本清洗函数

    参数：
    text: 原始文本
    remove_html: 移除HTML标签 (默认True)
    remove_line_breaks: 处理换行符 (默认True)
    remove_control_chars: 移除控制字符 (默认True)
    normalize_whitespace: 规范化空白字符 (默认True)
    remove_special_symbols: 移除特殊符号 (默认True)
    full_to_half: 全角转半角 (默认True)
    remove_emoji: 移除emoji (默认True)
    keep_common_punct: 保留常见标点 (默认True)
    """

    # 预编译正则表达式（性能优化）
    line_break_pattern = re.compile(r'[\r\n\t]') if remove_line_breaks else None
    control_chars_pattern = re.compile(r'[\x00-\x1f\x7f-\x9f]') if remove_control_chars else None
    whitespace_pattern = re.compile(r'\s+') if normalize_whitespace else None
    special_symbol_pattern = re.compile(
        r'[^\u4e00-\u9fa5A-Za-z0-9\s。，？！、；：“”‘’（）().?;!\'\-]'
    ) if remove_special_symbols else None
    emoji_pattern = re.compile("["
                               u"\U0001F600-\U0001F64F"  # 表情符号
                               u"\U0001F300-\U0001F5FF"  # 符号和象形文字
                               u"\U0001F680-\U0001F6FF"  # 交通和地图符号
                               u"\U0001F1E0-\U0001F1FF"  # 国家旗帜
                               u"\U00002500-\U00002BEF"  # 中文/日文/韩文字符
                               u"\U00002702-\U000027B0"
                               "]+", flags=re.UNICODE) if remove_emoji else None
    url_pattern = re.compile(
        r'http[s]?://(?:[a-zA-Z]|[0-9]|[$-_@.&+]|[!*\\(\\),]|(?:%[0-9a-fA-F][0-9a-fA-F]))+'
    )

    # 提取并保护 URL
    urls = url_pattern.findall(text)
    url_placeholder = 'URL_{}'
    for i, url in enumerate(urls):
        text = text.replace(url, url_placeholder.format(i), 1)

    # 处理流程
    if remove_html:
        # 使用更安全的BeautifulSoup处理HTML
        soup = BeautifulSoup(text, "html.parser")
        text = soup.get_text(separator=' ').strip()

    if isinstance(text, bytes):
        text = text.decode('utf-8', 'ignore')

    # 处理顺序优化（先处理可能产生空白的内容）
    if line_break_pattern:
        text = line_break_pattern.sub(' ', text)

    if control_chars_pattern:
        text = control_chars_pattern.sub('', text)

    if full_to_half:
        # 全角转半角（保留中文标点）
        text = ''.join([unicodedata.normalize('NFKC', char)
                        if not ('\u3000' <= char <= '\u303f') else char
                        for char in text])

    if remove_special_symbols:
        # 保留中文、日文、韩文字符和常见标点
        text = special_symbol_pattern.sub('', text)

    if remove_emoji:
        text = emoji_pattern.sub('', text)

    if normalize_whitespace:
        text = whitespace_pattern.sub(' ', text).strip()

    # 恢复 URL
    for i, url in enumerate(urls):
        placeholder = url_placeholder.format(i)
        text = text.replace(placeholder, url)

    # 处理保留标点逻辑
    if keep_common_punct:
        # 保留常见中文和英文标点
        punct_pattern = re.compile(
            r'([\u3002\uff1f\uff01\uff0c\u3001\uff1b\uff1a\u201c\u201d\u2018\u2019\uff08\uff09\u300a\u300b\u3008\u3009\u3010\u3011\u300e\u300f\u300c\u300d\uff5e\uff01-\uff5e])')
        text = punct_pattern.sub(r' \1 ', text)  # 添加空格保护标点
        if normalize_whitespace:
            text = whitespace_pattern.sub(' ', text).strip()

    return text


if __name__ == '__main__':
    # 测试 `find_matching_strings` 函数
    content = "自然语言处理是人工智能的重要领域。"
    str_array = ["处理", "自然语言", "不存在"]
    print(find_matching_strings(content, str_array))  # 应输出：['处理', '自然语言']

    # 测试 `clean_text` 函数
    dirty_text = """
    <p>这是一个测试文本！&#13;
    Hello world！😊 
    【重要通知】请访问https://example.com
    价格：￥199.50 （原价$299）
    保留  多余空格&nbsp;测试
    特殊符号：▲►▼◄�×÷
    """
    print(clean_text(dirty_text))
