import json
import re
import json_repair


class JsonExtractor:
    def __init__(self):
        """
        初始化JsonExtractor。
        此类旨在从包含任意文本的字符串中提取出所有独立的、有效的JSON对象或数组。
        """
        pass

    def remove_think_blocks(self, text):
        """
        移除文本中所有被 ...</think> 包裹的内容。
        """
        # 正则表达式解释:
        #      : 匹配开头的 "" 标签
        # .*?         : 匹配任何字符 (.) 零次或多次 (*?)，但尽可能少地匹配 (非贪婪模式)
        #               这很重要， чтобы оно не захватило текст между двумя разными  блоками.
        # </think>    : 匹配结尾的 "</think>" 标签
        # re.DOTALL   : 使 '.' 特殊字符匹配包括换行符在内的任意字符。
        #               这对于处理多行的  块至关重要。
        pattern = r".*?</think>"
        cleaned_text = re.sub(pattern, "", text, flags=re.DOTALL)
        return cleaned_text

    def _parse_with_fallbacks(self, potential_json_str):
        """
        尝试使用标准json或json_repair解析器解析字符串。

        参数:
            potential_json_str (str): 需要解析的潜在JSON字符串

        返回:
            str/None: 如果解析成功返回原始字符串，否则返回None
        """
        if not potential_json_str or not potential_json_str.strip():
            return None
        try:
            json.loads(potential_json_str)
            return potential_json_str
        except json.JSONDecodeError:
            try:
                json_repair.loads(potential_json_str)
                return potential_json_str
            except Exception:
                return None

    def _filter_substrings(self, string_list):
        """
        过滤列表中作为其他字符串子串的JSON字符串。

        参数:
            string_list (List[str]): 包含可能重复/嵌套的JSON字符串列表

        返回:
            List[str]: 去重后的JSON字符串列表（无子串包含关系）
        """
        if not string_list or len(string_list) < 2:
            return string_list
        indexed_strings = sorted(enumerate(string_list), key=lambda x: len(x[1]), reverse=True)
        final_strings_with_indices = []
        for original_index_s1, s1 in indexed_strings:
            is_substring = False
            for _, s2_data in final_strings_with_indices:
                if s1 in s2_data and s1 != s2_data:
                    is_substring = True
                    break
            if not is_substring:
                final_strings_with_indices.append((original_index_s1, s1))
        final_strings_with_indices.sort(key=lambda x: x[0])
        return [s for _, s in final_strings_with_indices]

    def _extract_robustly(self, text):
        """
        策略1：使用括号/花括号平衡法在文本中稳健地查找并提取所有JSON片段。

        参数:
            text (str): 需要处理的原始文本

        返回:
            List[str]: 提取到的有效JSON字符串列表（可能包含子串）
        """
        json_strings = []
        current_pos = 0  # 当前在文本中的扫描起始位置
        text_len = len(text)

        while current_pos < text_len:
            # 查找下一个 '{' 或 '['，从 current_pos 开始
            next_brace_idx = text.find('{', current_pos)
            next_bracket_idx = text.find('[', current_pos)

            start_idx = -1  # 当前找到的JSON片段的起始绝对索引
            start_char = ''
            end_char = ''

            # 确定先找到的是 '{' 还是 '['
            if next_brace_idx != -1 and (next_bracket_idx == -1 or next_brace_idx < next_bracket_idx):
                start_idx = next_brace_idx
                start_char = '{'
                end_char = '}'
            elif next_bracket_idx != -1 and (next_brace_idx == -1 or next_bracket_idx < next_brace_idx):
                start_idx = next_bracket_idx
                start_char = '['
                end_char = ']'
            else:
                break  # 在剩余文本中没有找到起始符

            balance = 0
            # 从找到的 start_idx 开始扫描，寻找匹配的结束符
            # i 是内层扫描的当前绝对索引
            i = start_idx
            while i < text_len:
                char_at_i = text[i]

                # 处理字符串常量，跳过其内部的括号
                if char_at_i == '"' or char_at_i == "'":
                    quote_char = char_at_i
                    # 寻找字符串的结束引号
                    str_content_end = i + 1
                    while str_content_end < text_len:
                        # 如果是结束引号且没有被转义
                        if text[str_content_end] == quote_char and text[str_content_end - 1] != '\\':
                            # 还要处理 \\" 这种其实不是转义的情况
                            if text[str_content_end - 1] == '\\' and str_content_end > i + 1 and text[
                                str_content_end - 2] == '\\':
                                pass  # 这是字面反斜杠+引号，继续
                            else:
                                break  # 找到了真正的结束引号
                        # 如果是 \\" (两个反斜杠加引号), 则不是转义的引号，是字面反斜杠和引号
                        # (上面的条件已经部分处理，这里是为了更清晰)
                        str_content_end += 1

                    if str_content_end < text_len:
                        i = str_content_end  # 将 i 移动到字符串结束引号的位置
                        # 下一次外层 while 循环的 i++ 会使其指向引号后的字符
                    else:
                        # 字符串未闭合，这个从 start_idx 开始的尝试可能无效
                        # 我们应该跳出这个内层while，让外层while从 start_idx + 1 尝试
                        current_pos = start_idx + 1  # 强制外层循环前进
                        i = text_len  # 终止内层循环
                        balance = -1  # 标记为无效，避免后续解析
                        break  # 跳出内层 while

                elif char_at_i == start_char:
                    balance += 1
                elif char_at_i == end_char:
                    balance -= 1

                if balance == 0:  # 找到了一个平衡的块
                    potential_json_str = text[start_idx: i + 1]
                    parsed_str = self._parse_with_fallbacks(potential_json_str)
                    if parsed_str:
                        json_strings.append(parsed_str)
                        current_pos = i + 1  # 下次外层扫描从这个JSON之后开始
                        # 不需要 break 内层 while，因为我们已经处理完这个片段
                        # 外层 while 的 current_pos < text_len 会判断是否继续
                        i = text_len  # 强制结束内层循环，因为这个JSON已处理
                        break  # 跳出内层 while
                    else:
                        # 虽然平衡了，但无法解析。这可能是个更大的JSON的一部分，
                        # 或者只是碰巧平衡的非JSON。内层循环会继续，
                        # 尝试找到一个从 start_idx 开始的、更大的、可解析的平衡块。
                        # 如果内层循环到末尾都没找到，外层循环的 current_pos 会从 start_idx + 1 开始。
                        pass
                i += 1  # 内层循环索引递增

            # 如果内层循环是因为 i >= text_len 而结束，并且 balance != 0 或没有成功解析
            # (即 found_json_in_segment 场景，但这里没有这个flag了)
            # 我们需要确保 current_pos 被正确设置以进行下一次查找
            if balance != 0:  # 如果到文本末尾都没平衡，或者因字符串未闭合跳出
                # 并且没有在上面成功解析并设置 current_pos
                if current_pos <= start_idx:  # 避免 current_pos 未被有效更新
                    current_pos = start_idx + 1
            elif current_pos <= start_idx and balance == 0:  # 平衡了但没解析成功，内层循环走完
                current_pos = start_idx + 1

        return self._filter_substrings(json_strings)

    def _extract_with_simple_regex(self, text):
        """
        策略2：使用正则表达式提取文本中所有可能的JSON对象或数组。

        参数:
            text (str): 需要处理的原始文本

        返回:
            List[str]: 提取到的有效JSON字符串列表（可能包含子串）
        """
        json_strings_from_regex = []
        pattern = r'(\{.*?\})|(\[.*?\])'
        for match_obj in re.finditer(pattern, text, re.DOTALL):
            potential_json_str = match_obj.group(0)
            parsed_str = self._parse_with_fallbacks(potential_json_str)
            if parsed_str:
                json_strings_from_regex.append(parsed_str)
        return self._filter_substrings(json_strings_from_regex)

    def extract(self, text_to_process):
        """
        主入口：从文本中提取所有有效的JSON对象或数组。

        参数:
            text_to_process (str): 需要处理的原始文本

        返回:
            List[str]: 提取到的有效JSON字符串列表（已去除子串）
        """
        if not text_to_process or not text_to_process.strip():
            return []
        extracted_jsons = self._extract_robustly(text_to_process)
        if not extracted_jsons:
            extracted_jsons_regex = self._extract_with_simple_regex(text_to_process)
            return extracted_jsons_regex
        return extracted_jsons


# --- 示例用法 ---
if __name__ == "__main__":
    extractor = JsonExtractor()

    print("--- 您的案例 (再次测试) ---")
    user_case_text_raw = '''<think>

</think>

```json
{
  "main_categories": ["国际关系", "经贸与贸易"],
  "sub_categories": ["中美经贸关系", "高层会谈"]
}
```'''
    print(f"原始输入:\n'{user_case_text_raw}'")
    result_user_case_raw = extractor.extract(user_case_text_raw)
    print(f"\n提取结果:")
    for item in result_user_case_raw:
        print(item)
    print("-" * 20)  # 期望输出完整的JSON对象

    print("\n--- 另一个复杂案例 ---")
    complex_text = '''无关文本 {"key1": "val1", "nested": {"nk1": [1, "str", {"deep": true}]}} 无关文本。
    [{"id":1, "name":"item1"}, {"id":2, "name":"item2"}]
    还有一些 { "simple": "object" } 在这里。
    <think>
    ```json
    {"this_is_inside_think_and_codeblock": "should_be_found_if_not_preprocessed"}
    ```
    </think>
    '''
    print(f"输入:\n'{complex_text}'")
    result_complex = extractor.extract(complex_text)
    print(f"\n提取结果:")
    for item in result_complex:
        print(item)
    print("-" * 20)

    print("\n--- 包含转义字符和引号的字符串 ---")
    text_with_escapes = '{"description": "A string with \\"escaped quotes\\" and a backslash \\\\.", "path": "C:\\\\Users\\\\<USER>\n'{text_with_escapes}'")
    result_escapes = extractor.extract(text_with_escapes)
    print(f"\n提取结果:")
    for item in result_escapes:
        print(item)
    print("-" * 20)

    print("\n--- 纯粹的文本中散布JSON ---")
    text_scattered_json = '前缀 {"key": "value", "arr": [1,2,{"sub_key":"sub_val"}]} 中间文本 {"another_key": true, "data": null} 后缀 [10, 20, "end"]'
    print(f"输入:\n'{text_scattered_json}'")
    result_scattered = extractor.extract(text_scattered_json)
    print(f"提取结果:")
    for item in result_scattered:
        print(item)
    print("-" * 20)
