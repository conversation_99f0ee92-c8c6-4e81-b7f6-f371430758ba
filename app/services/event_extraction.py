"""
事件提取服务模块

提供简单易用的事件提取功能，封装了底层的 LangChain 实现。
"""

from typing import List, Dict, Any, Optional

from app.core.logger_config import get_logger
from app.llm_integrations.chains.event_extraction_chain import EventExtraction<PERSON>hain
from app.llm_integrations.configs.event_extraction_config import EventExtractionConfig
from app.llm_integrations.llm_models import LlmModel
from app.llm_integrations.parsers.event_extraction_parser import EventExtractionOutputParser
from app.llm_integrations.prompts import map_prompt
from app.llm_integrations.schemas.event_extraction_schemas import EventExtractionResult
from app.utils.str_utils import clean_text

logger = get_logger()


class EventExtractionService:
    """
    事件提取服务类

    提供事件提取的高级接口，包括单文本提取、批量提取等功能。
    """

    def __init__(self):
        """初始化事件提取服务"""
        self._chain = None
        logger.info("EventExtractionService 初始化完成")

    @property
    def chain(self) -> EventExtractionChain:
        """懒加载获取事件提取链"""
        if self._chain is None:
            logger.info("创建新的事件提取Chain实例")
            self._chain = EventExtractionChain(
                llm=LlmModel().get_openai_compatible_llm(),
                prompt=map_prompt,
                output_parser=EventExtractionOutputParser(),
            )
            logger.info("EventExtractionChain 已创建")
        return self._chain

    def extract_events(self, text: str, prompt_variables: Optional[Dict[str, Any]] = None) -> EventExtractionResult:
        """
        从文本中提取事件信息

        Args:
            text (str): 需要提取事件的文本
            prompt_variables (Optional[Dict[str, Any]]): 用于格式化prompt的额外变量

        Returns:
            EventExtractionResult: 提取到的事件信息
        """
        try:
            logger.info(f"开始提取事件，文本长度: {len(text)}")
            cleaned_text = clean_text(text, keep_common_punct=False)
            logger.info(f"文本清洗后长度: {len(cleaned_text)}")
            events_result: EventExtractionResult = self.chain.extract_events(
                cleaned_text,
                prompt_variables=prompt_variables
            )
            logger.info(f"成功提取 {events_result.total} 个事件")
            return events_result
        except Exception as e:
            logger.error(f"事件提取失败: {str(e)}")
            raise


# 创建全局服务实例
event_extraction_service = EventExtractionService()


# 便捷函数
def extract_events(text: str, prompt_variables: Optional[Dict[str, Any]] = None) -> EventExtractionResult:
    """
    便捷函数：提取事件信息

    Args:
        text (str): 需要提取事件的文本
        prompt_variables (Optional[Dict[str, Any]]): 用于格式化prompt的额外变量

    Returns:
        Dict[str, Any]: 提取到的事件信息
    """
    return event_extraction_service.extract_events(text, prompt_variables=prompt_variables)
