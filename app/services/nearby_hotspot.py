"""
周边热点事件提取服务模块

提供简单易用的周边热点事件提取功能，封装了底层的 LangChain 实现。
"""

from typing import List, Dict, Any, Optional

from app.core.logger_config import get_logger
from app.llm_integrations.chains.nearby_hotspot_chain import NearbyHotspot<PERSON>hain
from app.llm_integrations.configs.nearby_hotspot_config import NearbyHotspotConfig
from app.llm_integrations.llm_models import LlmModel
from app.llm_integrations.parsers.nearby_hotspot_parser import NearbyHotspotOutputParser
from app.llm_integrations.prompts import nearby_hotspot_map_prompt
from app.llm_integrations.schemas.nearby_hotspot_schemas import NearbyHotspotInfoResult
from app.utils.str_utils import clean_text

logger = get_logger()


class NearbyHotspotService:
    """
    周边热点事件提取服务类

    提供周边热点事件提取的高级接口，包括单文本提取、批量提取等功能。
    """

    def __init__(self):
        """初始化周边热点事件提取服务"""
        self._chain = None
        logger.info("NearbyHotspotService 初始化完成")

    @property
    def chain(self) -> NearbyHotspotChain:
        """懒加载获取周边热点事件提取链"""
        if self._chain is None:
            logger.info("创建新的周边热点事件提取Chain实例")
            self._chain = NearbyHotspotChain(
                llm=LlmModel().get_openai_compatible_llm(),
                prompt=nearby_hotspot_map_prompt,
                output_parser=NearbyHotspotOutputParser(),
            )
            logger.info("NearbyHotspotChain 已创建")
        return self._chain

    def extract_hotspots(self, text: str, prompt_variables: Optional[Dict[str, Any]] = None) -> NearbyHotspotInfoResult:
        """
        从文本中提取周边热点事件信息

        Args:
            text (str): 需要提取周边热点事件的文本
            prompt_variables (Optional[Dict[str, Any]]): 用于格式化prompt的额外变量

        Returns:
            NearbyHotspotInfoResult: 提取到的周边热点事件信息
        """
        try:
            logger.info(f"开始提取周边热点事件，文本长度: {len(text)}")
            cleaned_text = clean_text(text, keep_common_punct=False)
            logger.info(f"周边热点事件文本清洗后长度: {len(cleaned_text)}")
            hotspots_result: NearbyHotspotInfoResult = self.chain.extract_hotspots(
                cleaned_text,
                prompt_variables=prompt_variables
            )
            logger.info(f"成功提取 {hotspots_result.total} 个周边热点事件")
            return hotspots_result
        except Exception as e:
            logger.error(f"周边热点事件提取失败: {str(e)}")
            raise


# 创建全局服务实例
nearby_hotspot_service = NearbyHotspotService()


# 便捷函数
def extract_hotspots(text: str, prompt_variables: Optional[Dict[str, Any]] = None) -> NearbyHotspotInfoResult:
    """
    便捷函数：提取周边热点事件信息

    Args:
        text (str): 需要提取周边热点事件的文本
        prompt_variables (Optional[Dict[str, Any]]): 用于格式化prompt的额外变量

    Returns:
        NearbyHotspotInfoResult: 提取到的周边热点事件信息
    """
    return nearby_hotspot_service.extract_hotspots(text, prompt_variables=prompt_variables)
