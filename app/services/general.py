"""
通用LLM调用服务模块

提供简单易用的通用LLM调用功能，封装了底层的 LangChain 实现。
支持动态prompt变量注入，适用于任何类型的LLM处理任务。
"""

from typing import List, Dict, Any, Optional

from app.core.logger_config import get_logger
from app.llm_integrations.chains.general_chain import GeneralChain
from app.llm_integrations.configs.general_config import GeneralConfig
from app.llm_integrations.llm_models import LlmModel
from app.llm_integrations.parsers.general_parser import GeneralOutputParser
from app.llm_integrations.prompts.general_prompt import map_prompt
from app.llm_integrations.schemas.general_schemas import GeneralInfoResult
from app.utils.str_utils import clean_text

logger = get_logger()


class GeneralService:
    """
    通用LLM调用服务类

    提供通用LLM调用的高级接口，支持动态prompt变量注入。
    这个服务类封装了GeneralChain的复杂性，为上层API提供简洁的调用接口。
    """

    def __init__(self):
        """初始化通用LLM调用服务"""
        self._chain = None
        logger.info("GeneralService 初始化完成")

    @property
    def chain(self) -> GeneralChain:
        """懒加载获取通用LLM调用链"""
        if self._chain is None:
            logger.info("创建新的通用LLM调用Chain实例")
            self._chain = GeneralChain(
                llm=LlmModel().get_openai_compatible_llm(),
                prompt=map_prompt,
                output_parser=GeneralOutputParser(),
            )
            logger.info("GeneralChain 已创建")
        return self._chain

    def process_text(self, text: str, prompt_variables: Optional[Dict[str, Any]] = None) -> GeneralInfoResult:
        """
        处理文本内容

        Args:
            text (str): 需要处理的文本
            prompt_variables (Optional[Dict[str, Any]]): 用于格式化prompt的额外变量
                预期包含 'map_prompt' 和 'combine_prompt' 键

        Returns:
            GeneralInfoResult: 处理结果

        Raises:
            ValueError: 当输入参数无效时
            Exception: 当处理过程中发生错误时
        """
        try:
            logger.info(f"开始通用LLM处理，文本长度: {len(text)}")
            
            # 验证输入
            if not text or not text.strip():
                raise ValueError("输入文本不能为空")
            
            # 验证prompt_variables
            if prompt_variables:
                self._validate_prompt_variables(prompt_variables)
            
            # 清理文本
            cleaned_text = clean_text(text, keep_common_punct=False)
            logger.info(f"文本清洗后长度: {len(cleaned_text)}")
            
            # 调用链处理
            result: GeneralInfoResult = self.chain.process_text(
                cleaned_text,
                prompt_variables=prompt_variables
            )
            
            logger.info(f"通用LLM处理成功，结果内容长度: {len(result.content)}")
            return result
            
        except Exception as e:
            logger.error(f"通用LLM处理失败: {str(e)}")
            raise

    def _validate_prompt_variables(self, prompt_variables: Dict[str, Any]) -> None:
        """
        验证prompt_variables的有效性

        Args:
            prompt_variables: 需要验证的prompt变量字典

        Raises:
            ValueError: 当prompt变量无效时
        """
        if not isinstance(prompt_variables, dict):
            raise ValueError("prompt_variables必须是字典类型")
        
        # 检查是否包含必要的prompt键
        required_keys = ['map_prompt', 'combine_prompt']
        missing_keys = []
        
        for key in required_keys:
            if key not in prompt_variables:
                missing_keys.append(key)
        
        if missing_keys:
            logger.warning(f"prompt_variables缺少推荐的键: {missing_keys}")
            # 不抛出异常，只是警告，因为这些键是可选的
        
        # 验证prompt内容不为空
        for key, value in prompt_variables.items():
            if key in required_keys and (not value or not str(value).strip()):
                logger.warning(f"prompt变量 '{key}' 为空或仅包含空白字符")

    async def aprocess_text(self, text: str, prompt_variables: Optional[Dict[str, Any]] = None) -> GeneralInfoResult:
        """
        异步处理文本内容

        Args:
            text (str): 需要处理的文本
            prompt_variables (Optional[Dict[str, Any]]): 用于格式化prompt的额外变量

        Returns:
            GeneralInfoResult: 处理结果
        """
        try:
            logger.info(f"开始异步通用LLM处理，文本长度: {len(text)}")
            
            # 验证输入
            if not text or not text.strip():
                raise ValueError("输入文本不能为空")
            
            # 验证prompt_variables
            if prompt_variables:
                self._validate_prompt_variables(prompt_variables)
            
            # 清理文本
            cleaned_text = clean_text(text, keep_common_punct=False)
            logger.info(f"文本清洗后长度: {len(cleaned_text)}")
            
            # 调用链异步处理
            result: GeneralInfoResult = await self.chain.aprocess_text(
                cleaned_text,
                prompt_variables=prompt_variables
            )
            
            logger.info(f"异步通用LLM处理成功，结果内容长度: {len(result.content)}")
            return result
            
        except Exception as e:
            logger.error(f"异步通用LLM处理失败: {str(e)}")
            raise

    def get_processing_stats(self) -> Optional[Dict[str, Any]]:
        """
        获取处理统计信息

        Returns:
            Optional[Dict[str, Any]]: 统计信息字典，如果链未初始化则返回None
        """
        if self._chain and self._chain.callback_handler:
            return self._chain.callback_handler.get_processing_summary()
        return None

    def reset_stats(self) -> None:
        """重置处理统计信息"""
        if self._chain and self._chain.callback_handler:
            # 重新创建回调处理器以重置统计
            from app.llm_integrations.callbacks.general_callback import GeneralCallback
            self._chain.callback_handler = GeneralCallback()
            logger.info("处理统计信息已重置")

    def validate_prompt_format(self, prompt_variables: Dict[str, Any]) -> Dict[str, Any]:
        """
        验证并格式化prompt变量

        Args:
            prompt_variables: 原始prompt变量

        Returns:
            Dict[str, Any]: 格式化后的prompt变量

        Raises:
            ValueError: 当prompt格式无效时
        """
        if not prompt_variables:
            raise ValueError("prompt_variables不能为空")
        
        formatted_variables = {}
        
        # 处理map_prompt
        if 'map_prompt' in prompt_variables:
            map_prompt_content = str(prompt_variables['map_prompt']).strip()
            if not map_prompt_content:
                raise ValueError("map_prompt不能为空")
            formatted_variables['map_prompt'] = map_prompt_content
        
        # 处理combine_prompt
        if 'combine_prompt' in prompt_variables:
            combine_prompt_content = str(prompt_variables['combine_prompt']).strip()
            if not combine_prompt_content:
                raise ValueError("combine_prompt不能为空")
            formatted_variables['combine_prompt'] = combine_prompt_content
        
        # 复制其他变量
        for key, value in prompt_variables.items():
            if key not in ['map_prompt', 'combine_prompt']:
                formatted_variables[key] = value
        
        logger.debug(f"prompt变量格式化完成，包含 {len(formatted_variables)} 个变量")
        return formatted_variables


# 创建全局服务实例
general_service = GeneralService()


# 便捷函数
def process_text(text: str, prompt_variables: Optional[Dict[str, Any]] = None) -> GeneralInfoResult:
    """
    便捷函数：处理文本内容

    Args:
        text (str): 需要处理的文本
        prompt_variables (Optional[Dict[str, Any]]): 用于格式化prompt的额外变量

    Returns:
        GeneralInfoResult: 处理结果
    """
    return general_service.process_text(text, prompt_variables=prompt_variables)


async def aprocess_text(text: str, prompt_variables: Optional[Dict[str, Any]] = None) -> GeneralInfoResult:
    """
    便捷函数：异步处理文本内容

    Args:
        text (str): 需要处理的文本
        prompt_variables (Optional[Dict[str, Any]]): 用于格式化prompt的额外变量

    Returns:
        GeneralInfoResult: 处理结果
    """
    return await general_service.aprocess_text(text, prompt_variables=prompt_variables)
