"""
前沿动态提取服务模块

提供简单易用的前沿动态提取功能，封装了底层的 LangChain 实现。
"""

from typing import List, Dict, Any, Optional

from app.core.logger_config import get_logger
from app.llm_integrations.chains.frontier_insights_chain import FrontierInsightsChain
from app.llm_integrations.configs.frontier_insights_config import FrontierInsightsConfig
from app.llm_integrations.llm_models import LlmModel
from app.llm_integrations.parsers.frontier_insights_parser import FrontierInsightsOutputParser
from app.llm_integrations.prompts import frontier_insights_map_prompt
from app.llm_integrations.schemas.frontier_insights_schemas import FrontierInsightsResult
from app.utils.str_utils import clean_text

logger = get_logger()


class FrontierInsightsService:
    """
    前沿动态提取服务类

    提供前沿动态提取的高级接口，包括单文本提取、批量提取等功能。
    """

    def __init__(self):
        """初始化前沿动态提取服务"""
        self._chain = None
        logger.info("FrontierInsightsService 初始化完成")

    @property
    def chain(self) -> FrontierInsightsChain:
        """懒加载获取前沿动态提取链"""
        if self._chain is None:
            logger.info("创建新的前沿动态提取Chain实例")
            self._chain = FrontierInsightsChain(
                llm=LlmModel().get_openai_compatible_llm(),
                prompt=frontier_insights_map_prompt,
                output_parser=FrontierInsightsOutputParser(),
            )
            logger.info("FrontierInsightsChain 已创建")
        return self._chain

    def extract_insights(self, text: str, prompt_variables: Optional[Dict[str, Any]] = None) -> FrontierInsightsResult:
        """
        从文本中提取前沿动态信息

        Args:
            text (str): 需要提取前沿动态的文本
            prompt_variables (Optional[Dict[str, Any]]): 用于格式化prompt的额外变量

        Returns:
            FrontierInsightsResult: 提取到的前沿动态信息
        """
        try:
            logger.info(f"开始提取前沿动态，文本长度: {len(text)}")
            cleaned_text = clean_text(text, keep_common_punct=False)
            logger.info(f"前沿动态文本清洗后长度: {len(cleaned_text)}")
            insights_result: FrontierInsightsResult = self.chain.extract_insights(
                cleaned_text,
                prompt_variables=prompt_variables
            )
            logger.info(f"成功提取 {insights_result.total} 个前沿动态")
            return insights_result
        except Exception as e:
            logger.error(f"前沿动态提取失败: {str(e)}")
            raise


# 创建全局服务实例
frontier_insights_service = FrontierInsightsService()


# 便捷函数
def extract_insights(text: str, prompt_variables: Optional[Dict[str, Any]] = None) -> FrontierInsightsResult:
    """
    便捷函数：提取前沿动态信息

    Args:
        text (str): 需要提取前沿动态的文本
        prompt_variables (Optional[Dict[str, Any]]): 用于格式化prompt的额外变量

    Returns:
        FrontierInsightsResult: 提取到的前沿动态信息
    """
    return frontier_insights_service.extract_insights(text, prompt_variables=prompt_variables)
