"""
回调相关的数据模型

定义回调请求和响应的数据结构，确保回调数据的一致性和可维护性。
"""

from datetime import datetime
from typing import Optional, Dict, Any
from pydantic import BaseModel, Field
from app.api.schemas.task_schemas import TaskType, TaskStatus


class CallbackTaskResult(BaseModel):
    """回调中的任务结果数据模型"""
    content: str = Field(..., description="原始文本内容")
    results: list = Field(..., description="提取的结果列表")
    time: str = Field(..., description="处理时间")
    total: int = Field(..., description="结果总数")


class CallbackSuccessData(BaseModel):
    """成功回调的数据模型"""
    task_id: str = Field(..., description="任务ID")
    task_type: TaskType = Field(..., description="任务类型")
    status: TaskStatus = Field(..., description="任务状态")
    result: CallbackTaskResult = Field(..., description="任务结果数据")
    submitted_at: str = Field(..., description="提交时间（ISO格式）")
    started_at: str = Field(..., description="开始时间（ISO格式）")
    completed_at: str = Field(..., description="完成时间（ISO格式）")
    processing_time: float = Field(..., description="处理耗时（秒）")
    echo: Optional[str] = Field(default=None, description="回显参数")

    class Config:
        """Pydantic配置"""
        json_encoders = {
            datetime: lambda v: v.isoformat()
        }


class CallbackFailureData(BaseModel):
    """失败回调的数据模型"""
    task_id: str = Field(..., description="任务ID")
    task_type: TaskType = Field(..., description="任务类型")
    status: TaskStatus = Field(..., description="任务状态")
    error_message: str = Field(..., description="错误信息")
    failed_at: str = Field(..., description="失败时间（ISO格式）")
    submitted_at: Optional[str] = Field(default=None, description="提交时间（ISO格式）")
    started_at: Optional[str] = Field(default=None, description="开始时间（ISO格式）")
    echo: Optional[str] = Field(default=None, description="回显参数")

    class Config:
        """Pydantic配置"""
        json_encoders = {
            datetime: lambda v: v.isoformat()
        }


class CallbackResponse(BaseModel):
    """回调接收方的响应模型（用于验证回调是否成功）"""
    status: str = Field(..., description="响应状态")
    message: str = Field(..., description="响应消息")
    received_at: str = Field(..., description="接收时间")
    task_id: Optional[str] = Field(default=None, description="任务ID")

    class Config:
        """Pydantic配置"""
        json_encoders = {
            datetime: lambda v: v.isoformat()
        }
