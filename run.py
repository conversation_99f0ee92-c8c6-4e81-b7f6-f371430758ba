import uvicorn
from app.core.config import settings


def main():
    """
    启动 FastAPI 应用的主函数。
    通过 Uvicorn 启动 FastAPI 应用，配置参数说明:
        - 模块路径: app.main 模块中的 app 实例
        - host: 从配置文件获取绑定主机地址
        - port: 从配置文件获取监听端口号
        - reload: 根据配置决定是否启用热重载
        - log_level: 将日志级别转换为小写格式以适配 Uvicorn 要求
        - access_log: 强制启用访问日志记录功能
    """
    uvicorn.run(
        "app.main:app",
        host=settings.HOST,
        port=settings.PORT,
        reload=settings.RELOAD,
        log_level=settings.LOG_LEVEL.lower(),
        access_log=True,
        # workers=1,  # 生产环境中可以设置多个 worker
    )


if __name__ == "__main__":
    main()
