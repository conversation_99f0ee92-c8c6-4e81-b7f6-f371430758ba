# LLMFlowHub API 文档

## 项目概述

**LLMFlowHub** 是一个智能信息处理与LLM能力集成的API中枢，提供文本分析和事件提取服务。

- **项目名称**: LLMFlowHub
- **版本**: 1.0.0
- **描述**: LLM 文本处理 API
- **技术栈**: FastAPI + Celery + Redis + LangChain + Pydantic V2

## 核心功能

- **多种提取能力**: 内置事件提取、前沿动态、周边热点等多种信息提取能力。
- **同步与异步处理**: 所有核心功能均支持同步实时调用和异步队列处理，满足不同场景需求。
- **动态Prompt模板**: 支持在API调用时传入自定义变量 (`prompt_variables`)，动态调整Prompt，极大增强了灵活性和适应性。([查看详细指南](./docs/DYNAMIC_PROMPT_VARIABLES.md))
- **长文本处理 (Map-Reduce)**: 内置对长文本的自动分割和汇总处理机制，可突破LLM的上下文长度限制。
- **完整的任务管理**: 提供任务提交、状态查询、结果获取、列表查询和取消等全套任务管理API。([查看任务接口文档](./README_TASKS.md))
- **健康检查与监控**: 提供对Redis、队列、Worker的健康检查接口，并可集成Flower进行可视化监控。

## 技术架构

- **Web框架**: FastAPI
- **异步任务**: Celery + Redis
- **LLM集成**: LangChain
- **数据验证**: Pydantic V2
- **日志系统**: Python logging
- **API文档**: OpenAPI/Swagger

## 快速开始

### 安装依赖
```bash
pip install -r requirements.txt
```

### 启动服务
```bash
# 启动API服务
python run.py

# 启动Celery Worker
python run_worker.py

# 启动Flower监控（可选）
python run_flower.py
```

### 访问API文档
- Swagger UI: http://localhost:8000/docs
- ReDoc: http://localhost:8000/redoc

## API接口详细说明

### 通用响应格式

所有API接口都使用统一的响应格式：

```json
{
  "status": "success|error",
  "msg": "响应消息",
  "data": {}, // 成功时的数据
  "error": "错误信息" // 失败时的错误信息
}
```

**响应字段说明**:
- `status` (string, 必需): 响应状态，值为 "success" 或 "error"
- `msg` (string, 可选): 响应消息描述
- `data` (object, 可选): 成功时返回的数据对象
- `error` (string, 可选): 失败时的错误信息

---

## 1. 健康检查接口

### GET /api/v1/health/

检查应用程序健康状态。

**请求参数**: 无

**响应示例**:
```json
{
  "status": "success",
  "msg": "健康检查成功",
  "data": {
    "name": "LLMFlowHub",
    "version": "1.0.0",
    "description": "LLM 文本处理 API"
  }
}
```

**响应数据字段**:
- `name` (string): 应用名称
- `version` (string): 应用版本
- `description` (string): 应用描述

---

## 2. 事件提取接口

### POST /api/v1/event-extraction/

从文本中同步提取事件信息。

**请求参数**:
```json
{
  "content": "昨天下午，苹果公司发布了新款iPhone。",
  "prompt_variables": {
    "language": "中文"
  }
}
```

**请求字段说明**:
- `content` (string, 必需): 需要提取事件的文本内容
- `prompt_variables` (object, 可选): 用于动态格式化Prompt的额外变量

**响应示例**:
```json
{
  "status": "success",
  "msg": "事件提取成功",
  "data": {
    "content": "昨天下午，苹果公司发布了新款iPhone。",
    "events": [
      {
        "type": "产品行为-发布",
        "trigger": "发布",
        "description": "苹果公司发布新款iPhone产品",
        "arguments": [
          {
            "role": "施事者",
            "values": ["苹果公司"]
          },
          {
            "role": "受事者", 
            "values": ["新款iPhone"]
          },
          {
            "role": "时间",
            "values": ["昨天下午"]
          }
        ]
      }
    ],
    "time": "2025-07-18 10:30:00",
    "total": 1
  }
}
```

**响应数据字段**:
- `content` (string): 原始输入文本
- `events` (array): 提取的事件列表
  - `type` (string): 事件类型分类
  - `trigger` (string): 事件触发词
  - `description` (string): 事件描述
  - `arguments` (array): 事件论元列表
    - `role` (string): 论元角色
    - `values` (array): 论元值列表
- `time` (string): 提取完成时间
- `total` (integer): 提取到的事件总数

---

## 3. 前沿动态提取接口

### POST /api/v1/frontier-insights/

从文本中提取前沿动态信息。

**请求参数**:
```json
{
  "content": "OpenAI发布了最新的GPT-4模型，在多项基准测试中表现优异。",
  "prompt_variables": {
    "domain": "人工智能"
  }
}
```

**请求字段说明**:
- `content` (string, 必需): 需要提取前沿动态的文本内容
- `prompt_variables` (object, 可选): 用于动态格式化Prompt的额外变量

**响应示例**:
```json
{
  "status": "success",
  "msg": "前沿动态提取成功",
  "data": {
    "content": "OpenAI发布了最新的GPT-4模型，在多项基准测试中表现优异。",
    "results": [
      {
        "type": "技术突破",
        "content": "OpenAI发布最新GPT-4模型，性能显著提升",
        "keywords": ["OpenAI", "GPT-4", "模型", "基准测试"],
        "hotness": "高"
      }
    ],
    "time": "2025-07-18 10:30:00",
    "total": 1
  }
}
```

**响应数据字段**:
- `content` (string): 原始输入文本
- `results` (array): 提取的前沿动态列表
  - `type` (string): 前沿动态分类
  - `content` (string): 动态内容描述
  - `keywords` (array): 关键词列表
  - `hotness` (string): 热度评估（高/中/低）
- `time` (string): 提取完成时间
- `total` (integer): 提取到的前沿动态总数

---

## 4. 周边热点事件提取接口

### POST /api/v1/nearby-hotspot/

从文本中提取周边热点事件信息。

**请求参数**:
```json
{
  "content": "中东地区发生军事冲突，可能影响全球石油供应。",
  "prompt_variables": {
    "output_format": "详细报告"
  }
}
```

**请求字段说明**:
- `content` (string, 必需): 需要提取周边热点事件的文本内容
- `prompt_variables` (object, 可选): 用于动态格式化Prompt的额外变量

**响应示例**:
```json
{
  "status": "success", 
  "msg": "周边热点事件提取成功",
  "data": {
    "content": "中东地区发生军事冲突，可能影响全球石油供应。",
    "results": [
      {
        "influence_area": "中东",
        "event_summary": "中东地区军事冲突影响石油供应",
        "threat_level": "高",
        "threat_analysis": "涉及直接军事冲突风险，可能冲击关键产业链"
      }
    ],
    "time": "2025-07-18 10:30:00",
    "total": 1
  }
}
```

**响应数据字段**:
- `content` (string): 原始输入文本
- `results` (array): 提取的周边热点事件列表
  - `influence_area` (string): 影响区域
  - `event_summary` (string): 事件摘要
  - `threat_level` (string): 威胁等级（高/中/低）
  - `threat_analysis` (string): 威胁等级评定依据
- `time` (string): 提取完成时间
- `total` (integer): 提取到的热点事件总数

---

## 5. 任务管理接口（异步处理）

任务管理接口提供异步处理能力，支持提交任务、查询状态、获取结果等功能。

### 5.1 提交任务

#### POST /api/v1/tasks/submit

提交异步处理任务。

**请求参数**:
```json
{
  "task_type": "event_extraction",
  "content": "昨天下午，苹果公司发布了新款iPhone。",
  "priority": 5,
  "callback_url": "https://your-domain.com/callback",
  "echo": "user_custom_id_12345",
  "prompt_variables": {
    "language": "英文"
  }
}
```

**请求字段说明**:
- `task_type` (string, 必需): 任务类型
  - `event_extraction`: 事件提取
  - `frontier_insights`: 前沿动态提取  
  - `nearby_hotspot`: 周边热点事件提取
- `content` (string, 必需): 需要处理的文本内容（1-50000字符）
- `priority` (integer, 可选): 任务优先级（1-10，数字越小优先级越高，默认5）
- `callback_url` (string, 可选): 任务完成后的回调URL
- `echo` (string, 可选): 回显参数，会在回调和结果中原样返回
- `prompt_variables` (object, 可选): 用于动态格式化Prompt的额外变量

**响应示例**:
```json
{
  "status": "success",
  "msg": "任务提交成功", 
  "data": {
    "task_id": "12345678-1234-1234-1234-123456789abc",
    "task_type": "event_extraction",
    "status": "PENDING",
    "submitted_at": "2025-07-18T14:30:00",
    "estimated_completion_time": null,
    "echo": "user_custom_id_12345"
  }
}
```

**响应数据字段**:
- `task_id` (string): 任务唯一标识符
- `task_type` (string): 任务类型
- `status` (string): 任务状态（PENDING/STARTED/SUCCESS/FAILURE/RETRY/REVOKED）
- `submitted_at` (string): 任务提交时间（ISO格式）
- `estimated_completion_time` (string, 可选): 预计完成时间
- `echo` (string, 可选): 回显参数

### 5.2 获取任务结果

#### GET /api/v1/tasks/result/{task_id}

获取任务结果和状态信息（合并接口）。

**路径参数**:
- `task_id` (string, 必需): 任务ID

**响应示例（任务成功）**:
```json
{
  "status": "success",
  "msg": "获取任务信息成功",
  "data": {
    "task_id": "12345678-1234-1234-1234-123456789abc",
    "task_type": "event_extraction", 
    "status": "SUCCESS",
    "result": {
      "content": "昨天下午，苹果公司发布了新款iPhone。",
      "events": [...],
      "time": "2025-07-18 10:30:00",
      "total": 1
    },
    "submitted_at": "2025-07-18T14:30:00",
    "started_at": "2025-07-18T14:30:05",
    "completed_at": "2025-07-18T14:30:15",
    "processing_time": 10.5,
    "error_message": null,
    "progress": 100.0,
    "retry_count": 0,
    "echo": "user_custom_id_12345"
  }
}
```

**响应示例（任务进行中）**:
```json
{
  "status": "success",
  "msg": "获取任务信息成功",
  "data": {
    "task_id": "12345678-1234-1234-1234-123456789abc",
    "task_type": "event_extraction",
    "status": "STARTED", 
    "result": null,
    "submitted_at": "2025-07-18T14:30:00",
    "started_at": "2025-07-18T14:30:05",
    "completed_at": null,
    "processing_time": null,
    "error_message": null,
    "progress": 50.0,
    "retry_count": 0,
    "echo": "user_custom_id_12345"
  }
}
```

**响应数据字段**:
- `task_id` (string): 任务ID
- `task_type` (string): 任务类型
- `status` (string): 任务状态
- `result` (object, 可选): 任务结果数据（仅成功时有值）
- `submitted_at` (string): 提交时间
- `started_at` (string, 可选): 开始时间
- `completed_at` (string, 可选): 完成时间
- `processing_time` (number, 可选): 处理耗时（秒）
- `error_message` (string, 可选): 错误信息
- `progress` (number, 可选): 任务进度（0-100）
- `retry_count` (integer): 重试次数
- `echo` (string, 可选): 回显参数

### 5.3 获取任务列表

#### GET /api/v1/tasks/list

获取任务列表，支持过滤和分页。

**查询参数**:
- `task_type` (string, 可选): 任务类型过滤
- `status` (string, 可选): 任务状态过滤
- `limit` (integer, 可选): 返回数量限制（1-100，默认20）
- `offset` (integer, 可选): 偏移量（默认0）

**请求示例**:
```
GET /api/v1/tasks/list?task_type=event_extraction&status=SUCCESS&limit=10&offset=0
```

**响应示例**:
```json
{
  "status": "success",
  "msg": "获取任务列表成功",
  "data": {
    "tasks": [
      {
        "task_id": "12345678-1234-1234-1234-123456789abc",
        "task_type": "event_extraction",
        "status": "SUCCESS",
        "progress": 100.0,
        "submitted_at": "2025-07-18T14:30:00",
        "started_at": "2025-07-18T14:30:05", 
        "completed_at": "2025-07-18T14:30:15",
        "error_message": null,
        "retry_count": 0,
        "echo": "user_custom_id_12345"
      }
    ],
    "total": 1,
    "limit": 10,
    "offset": 0
  }
}
```

**响应数据字段**:
- `tasks` (array): 任务状态列表
- `total` (integer): 总任务数
- `limit` (integer): 返回数量限制
- `offset` (integer): 偏移量

### 5.4 取消任务

#### POST /api/v1/tasks/cancel

取消指定任务。

**请求参数**:
```json
{
  "task_id": "12345678-1234-1234-1234-123456789abc",
  "reason": "用户主动取消"
}
```

**请求字段说明**:
- `task_id` (string, 必需): 要取消的任务ID
- `reason` (string, 可选): 取消原因

**响应示例**:
```json
{
  "status": "success",
  "msg": "任务取消成功",
  "data": {
    "task_id": "12345678-1234-1234-1234-123456789abc",
    "status": "REVOKED",
    "cancelled_at": "2025-07-18T14:35:00",
    "reason": "用户主动取消"
  }
}
```

**响应数据字段**:
- `task_id` (string): 任务ID
- `status` (string): 任务状态（REVOKED）
- `cancelled_at` (string): 取消时间
- `reason` (string, 可选): 取消原因

### 5.5 Redis健康检查

#### GET /api/v1/tasks/health/redis

检查Redis连接健康状态。

**请求参数**: 无

**响应示例**:
```json
{
  "status": "success",
  "msg": "Redis连接正常",
  "data": {
    "status": "success",
    "broker_connection": true,
    "result_backend_connection": true,
    "message": "所有Redis连接正常"
  }
}
```

### 5.6 队列健康检查

#### GET /api/v1/tasks/health/queue

检查队列健康状态和信息。

**请求参数**: 无

**响应示例**:
```json
{
  "status": "success", 
  "msg": "队列状态检查完成",
  "data": {
    "redis_status": {
      "status": "success",
      "broker_connection": true,
      "result_backend_connection": true
    },
    "queue_info": {
      "status": "success",
      "queues": {
        "celery": 0,
        "priority.high": 0,
        "priority.normal": 0,
        "priority.low": 0
      }
    },
    "worker_stats": {},
    "active_tasks": {}
  }
}
```

---

## 任务状态说明

| 状态 | 说明 |
|------|------|
| PENDING | 任务已提交，等待执行 |
| STARTED | 任务已开始执行 |
| SUCCESS | 任务执行成功 |
| FAILURE | 任务执行失败 |
| RETRY | 任务重试中 |
| REVOKED | 任务已被取消 |

## 错误处理

### 常见错误码

- `400`: 请求参数错误
- `404`: 资源不存在
- `500`: 服务器内部错误

### 错误响应示例

```json
{
  "status": "error",
  "msg": "请求参数错误",
  "error": "content字段不能为空"
}
```

## 使用示例

### Python示例

```python
import requests

# 1. 同步事件提取
response = requests.post('http://localhost:8000/api/v1/event-extraction/', 
                        json={'content': '苹果公司发布新产品'})
print(response.json())

# 2. 异步任务提交
response = requests.post('http://localhost:8000/api/v1/tasks/submit',
                        json={
                            'task_type': 'event_extraction',
                            'content': '苹果公司发布新产品',
                            'priority': 5
                        })
task_id = response.json()['data']['task_id']

# 3. 查询任务结果
result = requests.get(f'http://localhost:8000/api/v1/tasks/result/{task_id}')
print(result.json())
```

### cURL示例

```bash
# 健康检查
curl -X GET "http://localhost:8000/api/v1/health/"

# 事件提取
curl -X POST "http://localhost:8000/api/v1/event-extraction/" \
     -H "Content-Type: application/json" \
     -d '{"content": "苹果公司发布新产品"}'

# 提交异步任务
curl -X POST "http://localhost:8000/api/v1/tasks/submit" \
     -H "Content-Type: application/json" \
     -d '{
       "task_type": "event_extraction",
       "content": "苹果公司发布新产品",
       "priority": 5
     }'
```

## 部署说明

### 环境要求

- Python 3.10+
- Redis 服务器
- LLM API服务（如Ollama）

### 配置文件

主要配置在 `app/core/config.py` 中：

- `LLM_API_BASE`: LLM API地址
- `LLM_API_KEY`: LLM API密钥
- `LLM_MODEL_NAME`: 使用的模型名称
- `CELERY_BROKER_URL`: Redis连接地址
- `CELERY_RESULT_BACKEND`: 结果存储地址

### 生产部署

1. 使用Gunicorn启动API服务
2. 启动多个Celery Worker进程
3. 配置Nginx反向代理
4. 设置Redis持久化
5. 配置日志轮转

---

## 联系信息

如有问题或建议，请联系开发团队。

