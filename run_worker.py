import sys
from app.tasks.celery_app import celery_app
from app.core.config import settings
from app.core.logger_config import get_logger, setup_logging

logger = get_logger()


def main():
    """
    启动 LLMFlowHub 的 Celery Worker 进程。
    此脚本会自动检测操作系统，为 Windows 选择 'solo' pool，
    为其他系统（如 Linux/macOS）选择 'prefork' pool。
    """
    setup_logging("worker")
    try:
        logger.info("正在启动 LLMFlowHub Celery Worker...")

        # 1. 根据操作系统自动选择合适的 worker pool
        if sys.platform == "win32":
            pool_type = "solo"
            logger.info("检测到当前为 Windows 环境，将使用 'solo' pool。")
        else:
            # 'prefork' 是 Celery 在非 Windows 环境下的默认且性能最佳的模式
            pool_type = "prefork"
            logger.info(f"检测到当前为 {sys.platform} 环境，将使用 'prefork' pool。")

        # 2. 组合启动参数
        worker_args = [
            'worker',
            f'--pool={pool_type}',  # 使用上面判断得出的 pool 类型
            # (可选但推荐) 为 worker 设置一个明确的名称，便于识别和监控
            '--hostname=llmflowhub_worker@%h',
            '-E'  # 启用事件处理，用于监控和调试
        ]

        logger.info(f"Worker 启动参数: {' '.join(worker_args)}")
        logger.info(f"核心配置由 app.tasks.celery_app 定义")
        logger.info(f"- Redis Broker: {settings.REDIS_HOST}:{settings.REDIS_PORT}/{settings.REDIS_DB}")
        logger.info(f"- Redis Result Backend: {settings.REDIS_HOST}:{settings.REDIS_PORT}/{settings.REDIS_RESULT_DB}")

        # 3. 启动 Worker
        logger.info("Worker 启动中... (按 Ctrl+C 停止)")
        celery_app.worker_main(argv=worker_args)

    except KeyboardInterrupt:
        logger.info("收到键盘中断，Worker 正在停止...")
        sys.exit(0)
    except Exception as e:
        logger.error(f"启动 Worker 失败: {e}", exc_info=True)
        sys.exit(1)


if __name__ == "__main__":
    main()
