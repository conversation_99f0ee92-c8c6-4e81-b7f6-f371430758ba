# This file was autogenerated by uv via the following command:
#    uv pip compile pyproject.toml -o requirements.txt
amqp==5.3.1
    # via kombu
annotated-types==0.7.0
    # via pydantic
anyio==4.9.0
    # via
    #   httpx
    #   openai
    #   starlette
async-timeout==4.0.3
    # via
    #   langchain
    #   redis
beautifulsoup4==4.13.4
    # via llmflowhub (pyproject.toml)
billiard==4.2.1
    # via celery
celery==5.5.3
    # via
    #   llmflowhub (pyproject.toml)
    #   flower
celery-stubs==0.1.3
    # via llmflowhub (pyproject.toml)
certifi==2025.4.26
    # via
    #   httpcore
    #   httpx
    #   requests
charset-normalizer==3.4.2
    # via requests
click==8.2.1
    # via
    #   llmflowhub (pyproject.toml)
    #   celery
    #   click-didyoumean
    #   click-plugins
    #   click-repl
    #   uvicorn
click-didyoumean==0.3.1
    # via celery
click-plugins==1.1.1
    # via celery
click-repl==0.3.0
    # via celery
colorama==0.4.6
    # via
    #   click
    #   tqdm
distro==1.9.0
    # via openai
exceptiongroup==1.3.0
    # via anyio
fastapi==0.116.1
    # via llmflowhub (pyproject.toml)
flower==2.0.1
    # via llmflowhub (pyproject.toml)
greenlet==3.2.2
    # via sqlalchemy
h11==0.16.0
    # via
    #   httpcore
    #   uvicorn
httpcore==1.0.9
    # via httpx
httpx==0.28.1
    # via
    #   langsmith
    #   openai
humanize==4.12.3
    # via flower
idna==3.10
    # via
    #   anyio
    #   httpx
    #   requests
jiter==0.10.0
    # via openai
json-repair==0.47.7
    # via llmflowhub (pyproject.toml)
jsonpatch==1.33
    # via langchain-core
jsonpointer==3.0.0
    # via jsonpatch
kombu==5.5.4
    # via celery
langchain==0.3.26
    # via llmflowhub (pyproject.toml)
langchain-core==0.3.69
    # via
    #   llmflowhub (pyproject.toml)
    #   langchain
    #   langchain-openai
    #   langchain-text-splitters
langchain-openai==0.3.28
    # via llmflowhub (pyproject.toml)
langchain-text-splitters==0.3.8
    # via langchain
langsmith==0.3.45
    # via
    #   langchain
    #   langchain-core
mypy==1.16.1
    # via celery-stubs
mypy-extensions==1.1.0
    # via mypy
openai==1.88.0
    # via langchain-openai
orjson==3.10.18
    # via langsmith
packaging==24.2
    # via
    #   kombu
    #   langchain-core
    #   langsmith
pathspec==0.12.1
    # via mypy
prometheus-client==0.22.1
    # via flower
prompt-toolkit==3.0.51
    # via click-repl
pydantic==2.11.7
    # via
    #   llmflowhub (pyproject.toml)
    #   fastapi
    #   langchain
    #   langchain-core
    #   langsmith
    #   openai
    #   pydantic-settings
pydantic-core==2.33.2
    # via pydantic
pydantic-settings==2.10.1
    # via llmflowhub (pyproject.toml)
pyjwt==2.10.1
    # via llmflowhub (pyproject.toml)
pymysql==1.1.1
    # via llmflowhub (pyproject.toml)
python-dateutil==2.9.0.post0
    # via celery
python-dotenv==1.1.1
    # via
    #   llmflowhub (pyproject.toml)
    #   pydantic-settings
python-multipart==0.0.20
    # via llmflowhub (pyproject.toml)
pytz==2025.2
    # via flower
pyyaml==6.0.2
    # via
    #   langchain
    #   langchain-core
redis==5.2.1
    # via kombu
regex==2024.11.6
    # via tiktoken
requests==2.32.3
    # via
    #   langchain
    #   langsmith
    #   requests-toolbelt
    #   tiktoken
requests-toolbelt==1.0.0
    # via langsmith
six==1.17.0
    # via python-dateutil
sniffio==1.3.1
    # via
    #   anyio
    #   openai
soupsieve==2.7
    # via beautifulsoup4
sqlalchemy==2.0.41
    # via
    #   llmflowhub (pyproject.toml)
    #   langchain
    #   sqlalchemy-utils
sqlalchemy-utils==0.41.2
    # via llmflowhub (pyproject.toml)
starlette==0.46.2
    # via fastapi
tenacity==9.1.2
    # via
    #   llmflowhub (pyproject.toml)
    #   langchain-core
tiktoken==0.9.0
    # via langchain-openai
tomli==2.2.1
    # via mypy
tornado==6.5.1
    # via flower
tqdm==4.67.1
    # via openai
typing-extensions==4.14.0
    # via
    #   anyio
    #   beautifulsoup4
    #   celery-stubs
    #   exceptiongroup
    #   fastapi
    #   langchain-core
    #   mypy
    #   openai
    #   pydantic
    #   pydantic-core
    #   sqlalchemy
    #   typing-inspection
    #   uvicorn
typing-inspection==0.4.1
    # via
    #   pydantic
    #   pydantic-settings
tzdata==2025.2
    # via kombu
urllib3==2.4.0
    # via requests
uv==0.7.21
    # via llmflowhub (pyproject.toml)
uvicorn==0.35.0
    # via llmflowhub (pyproject.toml)
vine==5.1.0
    # via
    #   amqp
    #   celery
    #   kombu
wcwidth==0.2.13
    # via prompt-toolkit
zstandard==0.23.0
    # via langsmith
