# 定义服务器监听的主机地址，0.0.0.0表示监听所有地址
HOST=0.0.0.0
# 定义服务器的端口号
PORT=8000
# 定义API可接收的最大长度
MAX_CONTENT_LENGTH=100000
# 控制应用是否开启自动重载功能，True表示开启，有助于开发时实时查看改动
# False表示关闭，False关闭后，应用将不再自动重载，需要手动重启应用
RELOAD=False
# 定义日志记录的级别，INFO表示记录普通信息
# 可配置为DEBUG、INFO、WARNING、ERROR、CRITICAL
LOG_LEVEL=INFO
# 定义当前环境，development表示开发环境
# 可配置为development、production
ENVIRONMENT=production

# LLM配置，请填写基于OpenAI格式接口协议的LLM服务地址
LLM_API_BASE="https://api.openai.com/v1"
LLM_API_KEY="your_openai_api_key_here"
LLM_MODEL_NAME="gpt-3.5-turbo"

# MySQL配置
# MySQL服务器地址
MYSQL_HOST=localhost
# MySQL端口号，默认3306
MYSQL_PORT=3306
# MySQL数据库名称
MYSQL_DB=llmflowhub_db
# MySQL用户名
MYSQL_USER=root
# MySQL密码
MYSQL_PASSWORD=your_mysql_password

# Redis配置
REDIS_HOST=localhost
# Redis服务器端口号，默认6379
REDIS_PORT=6379
#  Redis 存储队列数据的数据库索引号，默认为0
REDIS_DB=0
# Redis 存储任务结果的数据库索引号，默认为1。注意：此数据库与任务队列数据库不能相同
REDIS_RESULT_DB=1
# Redis访问密码，留空表示无密码
REDIS_PASSWORD=

# 队列任务配置
# 任务最大重试次数，失败后最多重试3次
MAX_RETRIES=3
# 重试延迟时间，单位秒，即两次重试之间间隔60秒
RETRY_DELAY=60
# 任务超时时间，单位秒，即任务执行超过1小时将被终止
TASK_TIMEOUT=3600
# 最大并发任务数，同时最多执行4个任务
MAX_CONCURRENT_TASKS=1
# 结果缓存时间，单位秒，即任务结果保存24小时
RESULT_CACHE_TIME=86400

# 回调配置
# 回调请求超时时间，单位秒，即回调接口30秒内未响应视为超时
CALLBACK_TIMEOUT=30
# 回调最大重试次数，回调失败后最多重试3次
CALLBACK_RETRIES=3
# 回调重试延迟时间，单位秒，即两次回调重试之间间隔10秒
CALLBACK_RETRY_DELAY=10

