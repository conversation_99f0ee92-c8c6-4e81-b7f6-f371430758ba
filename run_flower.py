#!/usr/bin/env python3
"""
Flower 监控界面启动脚本 (使用 subprocess 以保证环境纯净)
"""

import sys
import subprocess
from app.core.config import settings
from app.core.logger_config import get_logger

logger = get_logger()


def main():
    """
    使用 subprocess 启动 Flower，这能完美模拟命令行行为，避免上下文冲突。
    """
    try:
        logger.info("正在准备启动 Flower 监控界面...")

        # Flower 配置参数
        flower_port = getattr(settings, 'FLOWER_PORT', 5555)
        flower_host = getattr(settings, 'FLOWER_HOST', '0.0.0.0')

        # 构建与你在命令行中使用的完全相同的命令
        # 注意: 'celery' 命令需要能被系统找到，
        # 这在激活了虚拟环境的情况下是默认满足的。
        command = [
            'celery',
            '-A', 'app.tasks.celery_app', # 指定 Celery app 实例
            'flower',
            f'--port={flower_port}',
            f'--address={flower_host}',
            # '--basic_auth=admin:admin123',
            # '--url_prefix=flower'
        ]

        logger.info(f"将要执行的命令: {' '.join(command)}")
        logger.info(f"Flower 将在 http://{flower_host}:{flower_port}/flower 上可用")
        
        # 使用 subprocess.run 启动 Flower
        # 这会阻塞，直到 Flower 进程被终止 (例如通过 Ctrl+C)
        process = subprocess.run(command, check=True)

    except subprocess.CalledProcessError as e:
        logger.error(f"Flower 进程异常退出，返回码: {e.returncode}")
        sys.exit(1)
    except KeyboardInterrupt:
        logger.info("收到键盘中断，脚本将退出 (Flower 进程已停止)。")
        sys.exit(0)
    except FileNotFoundError:
        logger.error("错误: 'celery' 命令未找到。")
        logger.error("请确保你已经激活了正确的虚拟环境，并且 celery 已经安装。")
        sys.exit(1)
    except Exception as e:
        logger.error(f"启动 Flower 失败: {str(e)}")
        sys.exit(1)


if __name__ == "__main__":
    main()
